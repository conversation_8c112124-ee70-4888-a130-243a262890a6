import moment from 'moment';
import Router from 'next/router';
import { get, replace, trim, truncate } from 'lodash';
import { error } from 'sharedComponents/Alerts/error';
import store from 'store';
import classNames from 'classnames';
import options from 'configuration/options.json';
const bcrypt = require('bcryptjs');

/**
 * classNames class merge
 * @param classes - class names
 * @returns {string} - class names joined by space
 */
export const mergeCls = (...classes) => {
  return classNames(...classes);
};

/**
 * Trim the name to remove spaces at the start and end, convert it to lowercase
 *and then capitalize the first letter.
 * @param {string} name - user name
 * @returns {string} capitalize AndTrim name
 */
export const capitalizeAndTrim = (name = '') => {
  let trimmedName = trim(name);
  if (trimmedName.length > 0) {
    trimmedName = trimmedName.charAt(0).toUpperCase() + trimmedName.slice(1);
  }
  return trimmedName;
};

/**
 * Check if the given URL is valid.
 *
 * @param {string} url - The URL to check.
 * @returns {boolean} - True if the URL is valid, false otherwise.
 */
export const isValidUrl = (url) => {
  const pattern = /^(https?:\/\/)?([\w.-]+)\.([a-z]{2,})(\/.*)?$/i;
  return pattern.test(url);
};

/**to open correct url
 * @param {string} url - url to be opened
 */
export const checkUrl = (url) => {
  if (!url || !isValidUrl(url)) {
    return;
  }
  let openUrl = url.startsWith('http') ? url : `https://${url}`;
  window.open(openUrl, '_blank');
};

/**
 * Make full name using first and last name
 *
 * @param {string}
 * @param {string}
 * @returns {string}
 */
export const getLogText = (data, path = 'logLine', maxLength = 250) => {
  const value = get(data, path);
  return value ? truncate(value, { length: maxLength }) : 'No log line';
};

/**
 * Make full name using first and last name
 *
 * @param {string} firstName - user first name
 * @param {string} lastName - user last name
 * @returns {string} fullName
 */
export const getFullName = (firstName = '', lastName = '') => {
  return `${capitalizeAndTrim(firstName)} ${capitalizeAndTrim(lastName)}`;
};

/**
 * Prevents the default behavior of the Enter key unless the focused element is a TEXTAREA.
 *
 * @param {Event} event - The event object.
 */
export const preventEnterKey = (event) => {
  // Check if the pressed key is Enter and the focused element is not a TEXTAREA
  if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
    // Prevent the default Enter key behavior (e.g., form submission)
    event.preventDefault();
  }
};

/**
 *
 * @param {*} item
 */

export const goToSnap = (item) => {
  if (!item) {
    return;
  }

  // First check for submissions.snapshot.hash (new API format)
  const hash = get(
    item,
    'snapshotHash',
    get(item, 'snapshot.hash', get(item, 'hash', get(item, 'slate.hash', ''))),
  );

  // Get notes
  const notes = get(item, 'notes', '');

  // Get IDs
  const slateId = get(item, '_id', '');
  const { id } = Router.query || {};

  // Get callout name to pass in query params
  const calloutName = get(
    item,
    'calloutName',
    get(item, 'callout.name', get(item, 'submissions.callout.name', '')),
  );

  if (hash) {
    // Build URL with parameters including calloutName
    const url = `/project/snap/${hash}?notes=${notes || ''}&id=${
      id || get(item, 'id') || ''
    }&slateId=${slateId || ''}&calloutName=${encodeURIComponent(
      calloutName || '',
    )}`;
    // Always open in a new window
    window.open(url, '_blank', 'noopener,noreferrer');
  }
};

/**
 *
 * @param {*} item
 * @returns
 */
export const renderCommonItem = (item) => {
  const snapCover = get(item, 'cover.coverPic', '');
  const basicInfo = get(item, 'basicInfo', false);
  const creatorUsername = get(item, 'creatorName', '');
  const title = get(item, 'cover.title');
  const projectTags = get(item, 'basicInfo.tags', []);
  const theme = get(item, 'theme');

  const coverUrl =
    snapCover ||
    (theme === 'dark' || theme === 'light'
      ? `${process.env.BucketUrl || ''}ASSETS/darkLightDmDefaultCover.jpg`
      : `${process.env.BucketUrl || ''}ASSETS/dmBcsDefaultCover.jpg`);

  return {
    coverUrl,
    title,
    creatorUsername,
    projectTags,
    basicInfo,
  };
};

/**
 *
 * @param {*} item
 * @returns
 */
export const parseSnapshotData = (item) => {
  let parsedData = {};
  try {
    parsedData = JSON.parse(get(item, 'projectSnapId.body', '{}'));
  } catch (err) {
    console.error('Error parsing snapshot body:', err);
  }

  return {
    ...item,
    ...parsedData,
  };
};

/**
 * Find organisation size using the organisation value
 *
 * @param {Event} event - The event object.
 */
export const calculateorgSize = (orgSize) => {
  const orgSizevalue = options.orgSize.find(
    (option) =>
      option.value.min === orgSize.min && option.value.max === orgSize.max,
  );
  return orgSizevalue ? orgSizevalue.key : '';
};

/**
 * Converts user data to a structured object for display in a table row.
 *
 * @param {Object} item - User data object.
 * @returns {Object} - Formatted user row data.
 */
export const userRowData = (item) => {
  const fullName = get(item, 'profile.name.fullName');
  const firstName = get(item, 'profile.name.firstName');
  const lastName = get(item, 'profile.name.lastName');

  let userName;

  if (!fullName) {
    userName = `${firstName || ''} ${lastName || ''}`.trim() || 'undefined';
  } else if (fullName) {
    userName = fullName;
  } else if (firstName || lastName) {
    userName = `${firstName || ''} ${lastName || ''}`.trim();
  } else {
    userName = 'undefined';
  }

  return {
    id: item._id,
    userName: userName,
    email: get(item, 'email'),
    projectNotifications: get(item, 'userMeta.acceptProjectNotifications'),
    city: get(item, 'profile.city.address'),
    termsPrivacy: get(item, 'userMeta.agreeTermsPrivacy'),
    receiveMarketMaterial: get(item, 'userMeta.receiveMarketMaterial'),
    created: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    updated: moment(get(item, 'updatedAt')).format('YYYY-MM-DD HH:mm:ss'),
    // subscriptionStatus: get(item, 'subscriptionStatus'),
    organisationName: get(item, 'profile.organisation'),
    organisationType: get(item, 'profile.organisationType'),
    organisationLogo: get(item, 'profile.organisationLogo'),
    organisationBio: get(item, 'profile.discovererProfile'),
    subscriptionStatus: get(item, 'userMeta.type'),
    organisationSize: calculateorgSize(
      get(item, 'profile.organisationSize', ''),
    ),
  };
};

/**
 * Converts project data to a structured object for display in a table row.
 *
 * @param {Object} item - Project data object.
 * @param {Object} userData - User data associated with the project.
 * @returns {Object} - Formatted project row data.
 */
export const projectRowData = (item) => {
  const tags = get(item, 'basicInfo.tags', []).map((tag) => tag.text);
  return {
    id: item._id,
    title: get(item, 'cover.title', 'undefined'),
    projectCreator: get(item, 'creator.username'),
    projectCreatorEmail: get(item, 'creator.email'),
    projectCreatorId: get(item, 'creator.userId'),
    regNo: get(item, 'regNo'),
    createdAt: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    updatedAt: moment(get(item, 'updatedAt')).format('YYYY-MM-DD HH:mm:ss'),
    status: get(item, 'basicInfo.status'),
    director: get(item, 'cover.director'),
    producer: get(item, 'cover.producer'),
    writer: get(item, 'cover.writer'),
    format: get(item, 'basicInfo.format'),
    genre: get(item, 'basicInfo.genre'),
    setting: get(item, 'basicInfo.setting'),
    projectTags:
      tags.length > 0
        ? `${tags[0]} ${tags.length > 1 ? `+ ${tags.length - 1} more` : ''}`
        : '',
    financePlan: get(item, 'totalFinanceSum', 0),
    budget: get(item, 'totalBudget', 0),
    unestimatedBudget: get(item, 'unestimatedBudget.amount'),
    treatment: get(item, 'projectDisc.treatment.url'),
    script: get(item, 'projectDisc.script.url'),
    lastSnapshotHash: get(item, 'lastSnapshot.hash'),
    logLine: get(item, 'basicInfo.logLine'),
    showStatus: get(item, 'unestimatedBudget.showStatus'),
    tags,
    subscriptionType: get(item, 'userMeta.type'),
  };
};
/**
 *
 * @param {*} list
 * @param {*} sortBy
 * @param {*} isAsc
 * @param {*} primarySortKey
 * @returns
 */
export const sortList = (list, sortBy, isAsc, primarySortKey = null) => {
  if (!list || list.length === 0) return [];

  return [...list].sort((a, b) => {
    if (primarySortKey && sortBy !== primarySortKey) {
      const primaryA = a[primarySortKey] || Infinity;
      const primaryB = b[primarySortKey] || Infinity;
      if (primaryA !== primaryB) {
        return isAsc ? primaryA - primaryB : primaryB - primaryA;
      }
    }

    if (sortBy === 'dateCreated') {
      const dateA = new Date(a.createdAt || 0);
      const dateB = new Date(b.createdAt || 0);
      return isAsc ? dateA - dateB : dateB - dateA;
    } else if (sortBy === 'name') {
      const nameA = (a.cover?.title || a.name || '').toLowerCase();
      const nameB = (b.cover?.title || b.name || '').toLowerCase();
      return isAsc ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
    }

    return 0;
  });
};

/**
 *
 * @param {*} snapshotList
 * @param {*} filters
 * @param {*} selectedSort
 * @param {*} isAscending
 * @returns
 */
export const applyFilters = (
  snapshotList,
  filters,
  selectedSort,
  isAscending,
) => {
  let filteredList = [...snapshotList];
  const {
    format,
    genre,
    setting,
    status,
    logLine,
    script,
    isLoglineOn,
    isScriptOn,
  } = filters;

  if (format)
    filteredList = filteredList.filter(
      (item) => get(item, 'basicInfo.format') === format,
    );

  if (genre)
    filteredList = filteredList.filter(
      (item) => get(item, 'basicInfo.genre') === genre,
    );
  if (setting)
    filteredList = filteredList.filter(
      (item) => get(item, 'basicInfo.setting') === setting,
    );
  if (status)
    filteredList = filteredList.filter(
      (item) => get(item, 'basicInfo.status') === status,
    );
  if (logLine)
    filteredList = filteredList.filter(
      (item) => get(item, 'basicInfo.logLine') === logLine,
    );
  if (script)
    filteredList = filteredList.filter(
      (item) => get(item, 'projectDisc.script') === script,
    );

  if (isLoglineOn)
    filteredList = filteredList.filter((item) =>
      Boolean(get(item, 'basicInfo.logLine', '')),
    );

  if (isScriptOn)
    filteredList = filteredList.filter((item) =>
      Boolean(get(item, 'projectDisc.script.name', '')),
    );

  return sortList(filteredList, selectedSort, isAscending);
};

/**
 * Converts callout data to a structured object for display in a table row.
 *
 * @param {*} item - Callout data object.
 * @returns {*} - Formatted callout row data.
 */
export const callOutListData = (item) => {
  return {
    id: item._id,
    callOutName: get(item, 'name', 'UNTITLED'),
    location: get(item, 'location'),
    opportunity: get(item, 'opportunities'),
    discovererName: get(item, 'discoverer.name'),
    discovererId: get(item, 'discoverer.id', null),
    genres: get(item, 'genres'),
    created: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    updatedAt: moment(get(item, 'updatedAt')).format('YYYY-MM-DD HH:mm:ss'),
    status: get(item, 'status'),
    isPublished: get(item, 'isPublished'),
    submission: get(item, 'submissions', []).length,
    slate: get(item, 'slates', []).length,
    body: get(item, 'body', {}),
    discovererEmail: get(item, 'discoverer.email'),
    organisationName: get(item, 'body.companyName'),
    hasActiveSubscription: get(item, 'hasActiveSubscription'),
    // subscriptionStatus: last(get(item, 'subscriptions', [])),
    subscriptionStatus: get(item, 'userMeta.type', ''),
  };
};

/**
 * Converts discoverer data to a structured object for display in a table row.
 * @param {*} item - Discoverer data object.
 * @returns {*} - Formatted discoverer row data.
 */
export const discovererRowData = (item) => {
  const users = get(item, 'user', {});
  return {
    id: users._id,
    discoverName: get(users, 'profile.name.fullName', ''),
    organisationName: get(users, 'profile.organisation', ''),
    email: get(users, 'email', ''),
    location: get(users, 'profile.city.address', ''),
    discovererProfile: get(users, 'profile.discovererProfile', ''),
    organisationLogo: get(users, 'profile.organisationLogo', ''),
    organisationLogoName: get(users, 'profile.organisationLogo', '')
      .split('/')
      .pop(),
  };
};

/**
 * Converts tag data to a structured object for display in a table row.
 *
 * @param {Object} item - Tag data object.
 * @returns {Object} - Formatted tag row data.
 */
export const tagsRowData = (item) => {
  return {
    id: item._id,
    text: get(item, 'text'),
  };
};

/**
 * Converts snap shpt data to a structured object for display in a table row.
 *
 * @param {Object} item - User data object.
 * @returns {Object} - Formatted user row data.
 */
export const snapShotRowData = (item) => {
  const title = get(item, 'notes', 'Unknown title');
  return {
    id: item._id,
    hash: get(item, 'hash'),
    snapShotTitle: title === '' ? 'Unknown title' : title,
    created: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    updated: moment(get(item, 'updatedAt')).format('YYYY-MM-DD HH:mm:ss'),
    status: get(item, 'publish') === true ? 'Published' : 'Draft',
    projectSnapshotAction: {
      id: item._id,
      hash: get(item, 'hash'),
      name: title === '' ? 'Unknown title' : title,
    },
  };
};

/**
 * Converts filters data to query parameters for backend filtering.
 *
 * @param {Object} filters - Filter data object.
 * @returns {Object} - Query parameters for backend filtering.
 */
export const handleFilters = (filters) => {
  const queryParameters = {};
  let fieldValueArray = [];

  for (const [key, value] of Object.entries(filters)) {
    switch (key) {
      case 'userName': {
        fieldValueArray.push(`profile.name.fullName|${value}`);
        break;
      }
      case 'user_subscription_type': {
        fieldValueArray.push(`userMeta.type|${value}`);
        break;
      }
      case 'createdAtBefore': {
        queryParameters['createdAt[$lte]'] = moment(value)
          .endOf('day')
          .toISOString();
        break;
      }
      case 'createdAtAfter': {
        queryParameters['createdAt[$gte]'] = moment(value)
          .startOf('day')
          .toISOString();
        break;
      }
      case 'updatedAtBefore': {
        queryParameters['updatedAt[$lte]'] = moment(value)
          .endOf('day')
          .toISOString();
        break;
      }
      case 'updatedAtAfter': {
        queryParameters['updatedAt[$gte]'] = moment(value)
          .startOf('day')
          .toISOString();
        break;
      }
      case 'basicInfo_status': {
        queryParameters['basicInfo.status'] = value;
        break;
      }
      case 'basicInfo_format': {
        queryParameters['basicInfo.format'] = value;
        break;
      }
      case 'basicInfo_genre': {
        queryParameters['basicInfo.genre'] = value;
        break;
      }
      case 'basicInfo_setting': {
        queryParameters['basicInfo.setting'] = value;
        break;
      }
      case 'treatment': {
        queryParameters['treatmentFilter'] = value;
        break;
      }
      case 'script': {
        queryParameters['scriptFilter'] = value;
        break;
      }
      case 'basicInfo_logLine': {
        queryParameters['logLineFilter'] = value;
        break;
      }
      case 'snapshot': {
        queryParameters['snapshotFilter'] = value;
        break;
      }
      default: {
        const field = replace(key, /_/g, '.');
        fieldValueArray.push(`${field}|${value}`);
        break;
      }
    }
  }
  if (fieldValueArray.length > 0) {
    queryParameters.$isearch = fieldValueArray.join(',');
  }

  if (
    queryParameters['createdAt[$lte]'] &&
    queryParameters['createdAt[$gte]']
  ) {
    const lte = queryParameters['createdAt[$lte]'];
    const gte = queryParameters['createdAt[$gte]'];
    queryParameters.createdAtFilter = JSON.stringify({
      $lte: lte,
      $gte: gte,
    });
    delete queryParameters['createdAt[$lte]'];
    delete queryParameters['createdAt[$gte]'];
  }

  if (
    queryParameters['updatedAt[$lte]'] &&
    queryParameters['updatedAt[$gte]']
  ) {
    const lte = queryParameters['updatedAt[$lte]'];
    const gte = queryParameters['updatedAt[$gte]'];
    queryParameters.updatedAtFilter = JSON.stringify({
      $lte: lte,
      $gte: gte,
    });
    delete queryParameters['updatedAt[$lte]'];
    delete queryParameters['updatedAt[$gte]'];
  }

  return queryParameters;
};

/**
 * Converts search data to query parameters for backend searching.
 *
 * @param {*} fields - fields to search
 * @param {*} q - search query
 * @returns {*} - search query
 */
export const handleSearchQuery = (text, fields) => {
  const searchQuery = {};
  if (text && fields.length > 0) {
    searchQuery.$q = `${text}|${fields.join(',')}`;
  }
  return searchQuery;
};

/**
 * Converts search data to query parameters for backend searching.
 *
 * @param {*} fields - fields to search
 * @param {*} q - search query
 * @returns {*} - search query
 */
export const handleSorting = (field, order) => {
  const orderType = order === 'DESC' ? -1 : 1;
  switch (field) {
    case 'id':
      return `_id|${orderType}`;
    case 'userName':
      return `profile.name.fullName|${orderType}`;
    case 'created':
      return `createdAt|${orderType}`;
    case 'city':
      return `profile.city.address|${orderType}`;
    case 'title':
      return `cover.title|${orderType}`;
    case 'projectCreator':
      return `creator.username|${orderType}`;
    case 'status':
      return `basicInfo.status|${orderType}`;
    case 'producer':
      return `cover.producer|${orderType}`;
    case 'director':
      return `cover.director|${orderType}`;
    case 'writer':
      return `cover.writer|${orderType}`;
    case 'format':
      return `cover.format|${orderType}`;
    case 'budget':
      return `totalBudget|${orderType}`;
    case 'financePlan':
      return `totalFinanceSum|${orderType}`;
    case 'projectCreatorEmail':
      return `creator.email|${orderType}`;
    case 'callOutName':
      return `name|${orderType}`;
    case 'discovererName':
      return `discoverer.name|${orderType}`;
    case 'submission':
      return `totalSubmissions|${orderType}`;
    case 'slate':
      return `totalSlates|${orderType}`;
    case 'organisationName':
      return `body.companyName|${orderType}`;
    default:
      return `${field}|${orderType}`;
  }
};

/**
 * Converts select data to query parameters for backend filtering.
 *
 * @param {*} select - fields to select
 * @returns {*} - select query
 */
export const handleSelect = (select) => {
  if (!select) return null;
  const selectQuery = {};
  if (select.length > 0) {
    selectQuery.$select = select.join(' ');
  }
  return selectQuery;
};

/**
 * Converts filter data to query parameters for backend filtering.
 *
 * @param {*} params - filter data
 * @param {*} fields - fields to search
 * @returns {*} - query parameters
 */
export const makeQueryParams = (params, fields = []) => {
  const { page, perPage } = get(params, 'pagination', {});
  const { field, order } = get(params, 'sort', {});
  const { select } = get(params, 'meta', {});
  const start = (page - 1) * perPage;
  const { q, ...filters } = params.filter;

  return {
    $limit: perPage,
    $skip: start,
    $sort: handleSorting(field, order),
    ...handleFilters(filters),
    ...handleSearchQuery(q, fields),
    ...handleSelect(select),
  };
};

/**
 * Compose project view data
 *
 * @param {Object} data - Data object containing project information.
 * @returns {Object} - Composed project view data.
 */
export const composeProjectViewData = (data) => {
  return {
    project: projectRowData(data.project),
    snapShots: data.snapShots.map((snapShot) => snapShotRowData(snapShot)),
    otherProjects: data.otherProjects.map((project) => projectRowData(project)),
  };
};

/**
 * Compose project view data
 *
 * @param {Object} data - Data object containing project information.
 * @returns {Object} - Composed project view data.
 */
export const composeUserViewData = (data) => {
  return {
    user: userRowData(data.user),
    userProjects: data.userProjects.map((project) => projectRowData(project)),
    userCallouts: data.userCallouts.map((callouts) =>
      composeCallOutListData(callouts),
    ),
  };
};

/**
 * Compose callout view data
 *
 * @param {Object} data - Data object containing callout information.
 * @returns {Object} - Composed callout view data.
 */
export const composeCallOutViewData = (data) => {
  return {
    callout: composeCallOutData(data),
    submissions: submissionList(get(data, 'submissions', [])),
    slates: slateList(get(data, 'slates', [])),
  };
};

/**
 * Compose calloutList view data
 *
 * @param {Object} data - Data object containing callout information.
 * @returns {Object} - Composed callout view data.
 */
export const composeCallOutData = (item) => {
  return {
    id: item._id,
    callOutName: get(item, 'name', 'UNTITLED'),
    location: get(item, 'location'),
    opportunity: get(item, 'opportunities'),
    discovererName: get(item, 'discoverer.name'),
    discovererId: get(item, 'discoverer.id'),
    genres: get(item, 'genres'),
    created: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    updatedAt: moment(get(item, 'updatedAt')).format('YYYY-MM-DD HH:mm:ss'),
    status: get(item, 'status'),
    isPublished: get(item, 'isPublished'),
    body: get(item, 'body', {}),
    discovererEmail: get(item, 'discoverer.email'),
    organisationName: get(item, 'body.companyName'),
    prompt: get(item, 'prompt'),
    // subscriptionStatus: get(item, 'subscriptionStatus', ''),
    subscriptionType: get(item, 'userMeta.type', ''),
    subscriptionStatus: get(item, 'userMeta.status', ''),
  };
};

/**
 * Converts callout data to a structured object for display in a table row of individual users.
 *
 * @param {*} item - Callout data object.
 * @returns {*} - Formatted callout row data.
 */
export const composeCallOutListData = (item) => {
  return {
    id: item._id,
    calloutName: get(item, 'name', 'UNTITLED'),
    discovererName: get(item, 'discoverer.name'),
    discovererId: get(item, 'discoverer.id'),
    organisationName: get(item, 'body.companyName'),
    genres: get(item, 'genres'),
    created: moment(get(item, 'createdAt')).format('YYYY-MM-DD HH:mm:ss'),
    slate: get(item, 'totalSlates', ''),
    submission: get(item, 'totalSubmissions', ''),
    callOutStatus: get(item, 'status'),
    callOutAction: {
      calloutId: item._id,
    },
  };
};

export const submissionList = (list) => {
  return list.map((item) => {
    const body = get(item, 'snapshot.body');
    const project = JSON.parse(body);
    return {
      id: get(project, '_id'),
      title: get(project, 'cover.title', ''),
      snapShotTitle: get(item, 'snapshot.title') || 'Unknown title',
      projectCreator: get(
        project,
        'creator.username',
        get(project, 'creator.email', ''),
      ),
      submissionStatus: get(item, 'status'),
      addedAt: moment(get(item, 'addedAt')).format('YYYY-MM-DD HH:mm:ss'),
      genre: get(project, 'basicInfo.genre', ''),
      projectCreatorId: get(project, 'creator.userId'),
      hash: get(item, 'snapshot.hash'),
      submissionAction: {
        projectTitle: get(project, 'cover.title', ''),
        submissionId: item._id,
        hash: get(item, 'snapshot.hash', ''),
        projectCreator: get(
          project,
          'creator.username',
          get(project, 'creator.email', ''),
        ),
      },
    };
  });
};

export const slateList = (list) => {
  return list.map((item) => {
    const body = get(item, 'snapshot.body');
    const project = JSON.parse(body);

    return {
      id: get(project, '_id'),
      title: get(project, 'cover.title') || 'Untitled',
      snapShotTitle: get(item, 'snapshot.title') || 'Unknown title',
      projectCreator: get(
        project,
        'creator.username',
        get(project, 'creator.email', ''),
      ),
      slateStatus: { status: get(item, 'status', ''), slateId: item._id },
      submissionAddedAt: moment(get(item, 'submissionAddedAt')).format(
        'YYYY-MM-DD HH:mm:ss',
      ),
      addedAt: moment(get(item, 'addedAt')).format('YYYY-MM-DD HH:mm:ss'),
      genre: get(project, 'basicInfo.genre', ''),
      projectCreatorId: get(project, 'creator.userId'),
      hash: get(item, 'snapshot.hash'),
      isEmailSent: get(item, 'isEmailSent') ? 'True' : 'False',
      slateAction: {
        projectTitle: get(project, 'cover.title', ''),
        slateId: item._id,
        hash: get(item, 'snapshot.hash', ''),
        projectCreator: get(
          project,
          'creator.username',
          get(project, 'creator.email', ''),
        ),
        notes: get(item, 'notes'),
      },
    };
  });
};

/**
 * Replace <p> tags with <h1> tags in a string.
 *
 * @param {*} htmlString - HTML string
 * @returns {*} - Updated HTML string
 */
export const replaceHtmlTags = (htmlString, tag) => {
  let updatedString = htmlString.replace(/<p>/g, `<${tag}>`);
  updatedString = updatedString.replace(/<\/p>/g, `</${tag}>`);
  return updatedString;
};

/**
 * Compose discoverer view data
 *
 * @param {*} roots - root elements
 * @param {*} data - data object
 * @returns {*} - Composed discoverer view data
 */
export const sanitizeEditorData = (roots, data) => {
  const initialData = {};
  for (const key in data) {
    if (data[key] && roots.includes(key)) {
      initialData[key] = data[key];
    }
  }
  return initialData;
};

/**
 * Remove HTML tags from a string.
 *
 * @param {*} html - HTML string
 * @returns {*} - String without HTML tags
 */
export const removeHtmlTags = (html) => {
  var doc = new DOMParser().parseFromString(html, 'text/html');
  return doc.body.textContent || '';
};

//bcrypt function
// Step 1: Generate a token with a key and today's date in UTC
export async function generateToken() {
  const key = process.env.byCryptTokenKey; // Ensure this environment variable is set

  const date = new Date().toISOString().split('T')[0]; // Get today's date in UTC (YYYY-MM-DD)
  const combinedString = key + date;
  const saltRounds = 10;
  const hashedToken = await bcrypt.hash(combinedString, saltRounds);

  return hashedToken;
}

/******filter projects ***********/

export const filterProjects = (data, filterCriteria) => {
  if (!Array.isArray(data)) {
    console.error('Data is not an array:', data);
    return [];
  }

  return data.filter((item) => {
    if (!item.snapshotId || !item.snapshotId.body) {
      return false;
    }

    let body;
    try {
      body = JSON.parse(get(item, 'snapshotId.body'));
    } catch (error) {
      return false;
    }
    return Object.keys(filterCriteria).every((key) => {
      const filterValue = filterCriteria[key];

      /**** Ignore empty filter values ****/
      if (
        filterValue === undefined ||
        filterValue === null ||
        filterValue === ''
      ) {
        return true;
      }

      /*****  get logLine value  ******/
      if (key === 'isLoglineOn') {
        const logLine = get(body, 'basicInfo.logLine', '');
        return filterValue ? Boolean(logLine) : true;
      }

      /***  get script name ***/
      if (key === 'isScriptOn') {
        const scriptName = get(body, 'projectDisc.script.name', '');
        return filterValue ? scriptName.length > 0 : true;
      }

      /*** Default case: match filter value ***/
      const value = get(body, `basicInfo.${key}`, '');
      return value === filterValue;
    });
  });
};

// Status mapping for visual representation
export const statusConfig = {
  FEEDBACK_SENT: {
    label: 'Feedback Sent',
    className: 'success',
    backgroundColor: '#00E5D5',
    textColor: '#05012D',
  },
  NEW: {
    label: 'Awaiting your response',
    className: 'warning',
    backgroundColor: '#1743D7',
    textColor: '#FFFFFF',
  },
  REJECTED: {
    label: 'Unsuccessful',
    className: 'error',
    backgroundColor: '#FF303D',
    textColor: '#FFFFFF',
  },
  TRACKING: {
    label: 'Tracking',
    className: 'warning',
    backgroundColor: '#EBFF29',
    textColor: '#05012D',
  },
  LETS_TALK: {
    label: 'Lets Talk',
    className: 'success',
    backgroundColor: '#00E5D5',
    textColor: '#05012D',
  },

  AWAITING_FEEDBACK: {
    label: 'Awaiting your response',
    className: 'warning',
    backgroundColor: '#1743D7',
    textColor: '#FFFFFF',
  },
  NOT_INTERESTED: {
    label: 'Unsuccessful',
    className: 'error',
    backgroundColor: '#FF303D',
    textColor: '#05012D',
  },
};

/***  File upload related functions ***/
const allowedImageExtensions = ['jpg', 'jpeg', 'png'];

export const getFileExtension = (filename = '') =>
  filename.split('.').pop()?.toLowerCase();

export const isImageExtensionValid = (file) => {
  const ext = getFileExtension(file.name);
  return allowedImageExtensions.includes(ext);
};

export const validateFile = (
  file,
  accept = allowedImageExtensions,
  maxSize = 5_000_000,
) => {
  const ext = getFileExtension(file.name);
  // If only PDF is accepted
  if (accept.length === 1 && accept[0].toLowerCase() === 'pdf') {
    if (ext !== 'pdf') {
      error('Only PDF files are allowed.');
      return false;
    }
    if (file.size > maxSize) {
      error(`File size exceeds ${(maxSize / 1_000_000).toFixed(1)} MB limit`);
      return false;
    }
    return true;
  }
  // Default: image logic
  if (!accept.includes(ext)) {
    error('Only JPG, JPEG, PNG files are allowed.');
    return false;
  }
  if (file.size > maxSize) {
    error(`File size exceeds ${(maxSize / 1_000_000).toFixed(1)} MB limit`);
    return false;
  }
  return true;
};

export const acceptFileTypeString = (accept = allowedImageExtensions) => {
  if (accept.length === 1 && accept[0].toLowerCase() === 'pdf') {
    return 'application/pdf';
  }
  return accept.map((ext) => `.${ext}`).join(',');
};

export const getSignedUrl = async (file) => {
  const token = JSON.parse(store.get('persist:auth')?.token || null);
  if (!token) {
    error('Authentication token not found');
    return null;
  }

  try {
    const res = await fetch(
      `${process.env.SmashApiBaseUrl}/v1/upload/generatePresignedUrl`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
          fileSize: file.size,
        }),
      },
    );

    const data = await res.json();
    return data?.data || null;
  } catch (err) {
    console.error(err);
    error('Failed to get upload URL');
    return null;
  }
};

// Combined function for slate section configs and allowed statuses
export const slateSectionConfig = {
  awaiting: {
    id: 'awaiting',
    heading: 'New Submissions Awaiting your Response',
    noProjectsMsg: 'No projects have been shared with you yet.',
    allowedStatus: ['AWAITING_FEEDBACK', 'NEW'],
    data: [],
  },
  letsTalk: {
    id: 'letsTalk',
    heading: 'Let’s talk',
    noProjectsMsg: 'No projects in lets talk.',
    allowedStatus: ['LETS_TALK'],
    data: [],
  },
  tracking: {
    id: 'tracking',
    heading: 'Tracking',
    noProjectsMsg: 'No projects in tracking.',
    allowedStatus: ['TRACKING'],
    data: [],
  },

  rejected: {
    id: 'rejected',
    heading: 'Not for me',
    noProjectsMsg: 'You have no projects in this section yet.',
    allowedStatus: ['NOT_INTERESTED'],
    data: [],
  },
};

// export const checkMissingRequiredFields = (
//   callout,
//   projectData,
//   sectionName,
// ) => {
//   if (!callout?.requiredFields || !projectData || !sectionName) return false;

//   // Map sidebar names to actual keys in requiredFields
//   const nameMapping = {
//     Cover: 'cover',
//     'Basic Information': 'basicInformation',
//     'Creative Team': 'creativeTeam',
//     Description: 'description',
//     'Cast Members': 'castMembers',
//     'Project Posters': 'projectPosters',
//     'Mood Board': 'moodboard',
//     'Project Videos': 'projectVideos',
//     'Comparable Projects': 'comparableProjects',
//     'Finance Plan': 'financePlan',
//     Budget: 'budget',
//     'Other Documents': 'otherDocuments',
//     'Sales Estimates': 'salesEstimate',
//   };

//   const keyName = nameMapping[sectionName] || sectionName;
//   const sectionFields = callout.requiredFields[keyName];

//   if (!sectionFields) return false;

//   return Object.keys(sectionFields).some((fieldKey) => {
//     if (!sectionFields[fieldKey]) return false;

//     const value = get(projectData, `${keyName}.${fieldKey}`);

//     return (
//       value === undefined ||
//       value === null ||
//       (typeof value === 'string' && value.trim() === '') ||
//       (Array.isArray(value) && value.length === 0)
//     );
//   });
// };

// Helper function to check if required fields are missing for a section
export const checkMissingRequiredFields = (
  callOutData,
  projectPreviewData,
  sectionName,
) => {
  if (!callOutData?.requiredFields || !projectPreviewData) return false;

  // Section mapping: sidebar name -> {requiredField: projectPath, isArray}
  const sectionMapping = {
    Cover: {
      requiredField: 'cover',
      projectPath: 'cover',
      fieldMapping: { coverImage: 'coverPic' },
    },
    'Basic Information': {
      requiredField: 'basicInformation',
      projectPath: 'basicInfo',
      fieldMapping: { runtime: 'runningTime' },
    },
    'Creative Team': {
      requiredField: 'creativeTeam',
      projectPath: 'creativeTeam',
      isArray: true,
    },
    Description: {
      requiredField: 'description',
      projectPath: 'projectDisc',
      fieldMapping: { treatmentBible: 'treatment.url', script: 'script.url' },
    },
    'Cast Members': {
      requiredField: 'castMembers',
      projectPath: 'castMembers',
      isArray: true,
    },
    'Mood Board': {
      requiredField: 'moodboard',
      projectPath: 'artWork',
      isArray: true,
    },
    'Project Videos': {
      requiredField: 'projectVideos',
      projectPath: 'videos',
      isArray: true,
    },
    'Finance Plan': {
      requiredField: 'financePlan',
      projectPath: 'financePlan',
      isArray: true,
    },
    Budget: {
      requiredField: 'budget',
      projectPath: 'budget',
      isArray: true,
    },
    'Other Documents': {
      requiredField: 'otherDocuments',
      projectPath: 'otherDocs',
      isArray: true,
    },
    'Sales Estimates': {
      requiredField: 'salesEstimate',
      projectPath: 'salesEstimateFile',
      isArray: true,
    },
  };

  const config = sectionMapping[sectionName];
  if (!config) return false;

  const requiredFieldConfig = callOutData.requiredFields[config.requiredField];
  if (!requiredFieldConfig) return false;

  // For array fields, just check if array is empty
  if (config.isArray) {
    const projectArray = projectPreviewData[config.projectPath] || [];
    return projectArray.length === 0;
  }

  // For object fields, check individual properties
  const projectSection = projectPreviewData[config.projectPath] || {};

  return Object.keys(requiredFieldConfig).some((fieldKey) => {
    if (!requiredFieldConfig[fieldKey]) return false;

    // Get the actual field name to check in project data
    const mappedField = config.fieldMapping?.[fieldKey] || fieldKey;

    // Handle nested paths like 'treatment.url'
    let value = projectSection;
    const pathParts = mappedField.split('.');
    for (const part of pathParts) {
      value = value?.[part];
    }

    return (
      value === undefined ||
      value === null ||
      (typeof value === 'string' && value.trim() === '') ||
      (Array.isArray(value) && value.length === 0)
    );
  });
};
