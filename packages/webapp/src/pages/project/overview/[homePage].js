import React, { PureComponent } from 'react';
import { connect } from 'react-redux';
import { reset } from 'redux-form';
import { withRouter } from 'next/router';
import { withTranslation } from 'react-i18next';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Loader from 'sharedComponents/loader';
import LeftSideBar from 'sharedComponents/LeftSideBar/leftSideBar';
import MobileBar from 'sharedComponents/LeftSideBar/mobileBar';
import Modal from 'sharedComponents/Modal/modal';
import { get } from 'lodash';
import PropTypes from 'prop-types';
import {
  setLoadingStatus,
  fetchProjectDetailsByID,
  updateProjectData,
  updateProjectBasicInfo,
  updateCreativeTeam,
  updateCoverInfo,
  setSaveButtonState,
  updateDescription,
  setSaveButtonStateBasicInfo,
  setSaveButtonStateDescription,
  setSaveButtonStateArtWork,
  updateProjectArtwork,
  setImageData,
  updateVideos,
  isFormStatus,
  removeSectionItems,
  updateCrewMembers,
  updateCompareProject,
  updateFinance,
  deleteFile,
  isBudgetOverflow,
  setVisibleSections,
  getHistory,
  updateSalesEstimation,
  setTheme,
  toggleTheme,
  deleteDescriptionFile,
  setEditSection,
  updateOtherDocuments,
  setDocsData,
  setCreatorTags,
  setSalesNewSnapStatus,
  setSnapSteps,
  shareProject,
  isGenerateQr,
  closeSnapStepsModal,
  setSnapRequest,
  setFinanceEditId,
  updateBudget,
  setCoverImageUrl,
  fetchDmProjects,
  updateSectionLocking,
  setProjectPreviewData,
  updateProjectPosters,
  updateUnestimatedBudget,
  setCoverImage,
  setCoverImageUploadStatus,
  setCollaboratorInfoModalStatus,
  setActiveGoals,
  updateGoals,
  setSnapInfoModalStatus,
  setSidebarStatus,
  setLeftSideBarActiveTab,
  setClickedItemOfSidebar,
  setIsClickedOnMenu,
  fetchCollaboratorList,
} from 'reducer/project';
import { getTagData, setTagData } from 'reducer/user';
import { setCollaboratorProjectId } from 'reducer/auth';
import { getCallOut } from 'reducer/callout';
import Socket from 'lib/socket';
import ObserverComponent from './observerComponent';
import Style from './style/projectDashboard.module.scss';
import Cover from './rightSplitComponents/cover/cover';
import BasicInfo from './rightSplitComponents/basicInfo/basicInfo';
import Team from './rightSplitComponents/creativeTeam';
import Description from './rightSplitComponents/description';
import Video from './rightSplitComponents/video';
import ProjectArtwork from './rightSplitComponents/projectArtwork/index';
import CastMembers from './rightSplitComponents/castMembers/index';
import CompareProject from './rightSplitComponents/compareProjects/index';
import FinancePlans from './rightSplitComponents/financePlans';
import Budget from './rightSplitComponents/budget/index';
import SalesEstimate from './rightSplitComponents/sales';
import UploadFile from './rightSplitComponents/uploadFiles';
import Poster from './rightSplitComponents/poster/index';
import BcsTheme from '../../template';
import CascadeTemplate from 'pages/cascadeTemplate/index';
import { setSubscriptionJourneyModal } from 'reducer/subscription';

// Only holds serverRuntimeConfig and process.env

class HomePage extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      visibleObserver: null,
    };
    this.cover = React.createRef();
    this.basicInfo = React.createRef();
    this.creativeTeam = React.createRef();
    this.projectDisc = React.createRef();
    this.videos = React.createRef();
    this.artWork = React.createRef();
    this.castMembers = React.createRef();
    this.share = React.createRef();
    this.comparableProject = React.createRef();
    this.financePlan = React.createRef();
    this.budget = React.createRef();
    this.salesEstimate = React.createRef();
    this.uploadFile = React.createRef();
    this.poster = React.createRef();
    this.refMap = {
      cover: this.cover,
      basicInfo: this.basicInfo,
      creativeTeam: this.creativeTeam,
      projectDisc: this.projectDisc,
      videos: this.videos,
      artWork: this.artWork,
      castMembers: this.castMembers,
      // share: this.share,
      comparableProject: this.comparableProject,
      financePlan: this.financePlan,
      budget: this.budget,
      salesEstimate: this.salesEstimate,
      uploadFile: this.uploadFile,
      poster: this.poster,
    };
    this.cascadeTemplate = {
      cover: this.cover,
      basicInfo: this.basicInfo,
      creativeTeam: this.creativeTeam,
      projectDisc: this.projectDisc,
      videos: this.videos,
      artWork: this.artWork,
      castMembers: this.castMembers,
      // share: this.share,
      comparableProject: this.comparableProject,
      financePlan: this.financePlan,
      budget: this.budget,
      salesEstimate: this.salesEstimate,
      uploadFile: this.uploadFile,
      poster: this.poster,
    };
  }

  async componentDidMount() {
    const {
      fetchProjectDetailsByID,
      router,
      getTagData,
      setSalesNewSnapStatus,
      fetchDmProjects,
      setCollaboratorProjectId,
      setProjectPreviewData,
      accessibleFeature,
      fetchCollaboratorList,
      subscriptionStatus,
      collaboratorList,
      userData,
      getCallOut,
    } = this.props;
    const id = router.asPath.split('/').pop();
    const mode = router.query.mode;
    const calloutId = router.query.calloutId;

    /* socket connect and implement */
    await fetchCollaboratorList(id);

    const isCollaborationLocked =
      !accessibleFeature.projectCollaborators ||
      subscriptionStatus === 'expired';
    const isCollaborator = collaboratorList.some(
      (user) => user.email === get(userData, 'email', ''),
    );

    if (isCollaborationLocked && isCollaborator) {
      router.push('/projects');
    }

    if (process.env.SocketEnabled === 'true') {
      const socket = Socket.connect(id);
      if (socket) {
        socket.on('sectionLock', function (response) {
          if (response) {
            setProjectPreviewData(response.data);
          }
        });
      }
    }
    this.updateSidebarVisibility();
    window.addEventListener('resize', this.updateSidebarVisibility);
    await setCollaboratorProjectId(null);
    // method to get tags from redux state
    await getTagData();
    await fetchProjectDetailsByID(id);
    await setSalesNewSnapStatus(false);
    await fetchDmProjects(id);

    // Fetch callout data if coming from callout route (both mode and calloutId present)
    if (mode === 'submitSnap' && calloutId) {
      await getCallOut(calloutId);
    }
  }

  componentWillUnmount() {
    window.removeEventListener('resize', this.updateSidebarVisibility);
    const socket = Socket.getInstance();
    if (socket) {
      socket.off('sectionLock');
    }
  }

  shouldComponentUpdate(nextProps) {
    if (nextProps) {
      return true;
    }
  }

  updateSidebarVisibility = () => {
    const { setSidebarStatus, setLeftSideBarActiveTab } = this.props;
    const screenWidth = window.innerWidth;
    const sidebarVisibility = screenWidth > 1040;
    if (!sidebarVisibility) {
      setLeftSideBarActiveTab('project');
    }
    setSidebarStatus(sidebarVisibility);
  };

  /* 
    This methos take as a argument tour id then set active goal
    and called tour on product fruits tours.
  */
  startTour = (/*tourId*/) => {
    // const { setActiveGoals, updateGoals, projectPreviewData } = this.props;
    // const projectId = get(projectPreviewData, '_id');
    // let activeGoals = false;
    // if (window !== undefined) {
    //   switch (tourId) {
    //     case 3140:
    //       activeGoals = 'goal-1';
    //       break;
    //     case 3138:
    //       activeGoals = 'goal-2';
    //       break;
    //     case 2301:
    //       activeGoals = 'goal-3';
    //       break;
    //     case 2439:
    //       activeGoals = 'goal-4';
    //       break;
    //     default:
    //       activeGoals = false;
    //   }
    //   setActiveGoals(activeGoals);
    //   updateGoals({ goal: activeGoals }, projectId);
    //   if (tourId) {
    //     window.productFruits.api.tours.tryStartTour(tourId);
    //   } else {
    //     window.productFruits.api.tours.disposeFn();
    //   }
    //   // Get all the tours
    //   //   const tours = window.productFruits.api.tours.getTours();
    // }
  };

  // this method is used for modal body
  body = () => {
    const { userData } = this.props;
    const firstName = get(userData, 'profile.name.firstName', '');
    const lastName = get(userData, 'profile.name.lastName', '');
    const fullName = `${firstName} ${lastName}`;
    return (
      <div>
        <div className="text-left">
          <p className="text-primary p2">
            You have been invited to collaborate on {fullName}
            {`'s`} project. You can go back to this project at any time from
            your “My Projects” page. Click continue to start collaborating now.
            project. You can go back to this project at any time from your 'My
            Projects' page. Click continue to start collaborating now.
          </p>
        </div>
      </div>
    );
  };

  visibleObserverSection = (section) => {
    const { setClickedItemOfSidebar, editSection } = this.props;
    !editSection && setClickedItemOfSidebar('');
    this.setState({ visibleObserver: section });
  };

  successBtnCallBack = (aiResponse) => {
    const { updateProjectBasicInfo, router } = this.props;
    const values = { logLine: aiResponse };
    const id = router.asPath.split('/').pop();
    updateProjectBasicInfo(values, id);
  };
  render() {
    const {
      accessibleFeature,
      t,
      isLoading,
      projectPreviewData,
      router,
      updateProjectData,
      updateProjectBasicInfo,
      coverData,
      updateCreativeTeam,
      updateCoverInfo,
      producerList,
      directorList,
      writerList,
      updateDescription,
      userData,
      setSaveButtonState,
      showButton,
      isDisable,
      setSaveButtonStateBasicInfo,
      showButtonBasic,
      showButtonDesc,
      showButtonArtwork,
      setSaveButtonStateDescription,
      setSaveButtonStateArtWork,
      updateProjectArtwork,
      setImageData,
      imageList,
      updateVideos,
      isFormStatus,
      isOpenForm,
      removeSectionItems,
      updateCrewMembers,
      updateCompareProject,
      updateProjectPosters,
      updateFinance,
      deleteFile,
      isBudgetOverflow,
      isBudgetModalOpen,
      setVisibleSections,
      visibleSections,
      historyList,
      getHistory,
      updateSalesEstimation,
      deleteDescriptionFile,
      setEditSection,
      updateOtherDocuments,
      docsList,
      setDocsData,
      setCreatorTags,
      tags,
      getTagData,
      tagList,
      setTagData,
      setSalesNewSnapStatus,
      salesNewSnapRequest,
      setSnapSteps,
      snapStepsStatus,
      shareProject,
      shareData,
      isGenerateQr,
      closeSnapStepsModal,
      loadData,
      snapRequest,
      setSnapRequest,
      setFinanceEditId,
      financeEditId,
      reset,
      updateBudget,
      coverImageUrl,
      setCoverImageUrl,
      notificationsList,
      updateSectionLocking,
      updateUnestimatedBudget,
      setCoverImage,
      coverImg,
      coverImgUploadStatus,
      setCoverImageUploadStatus,
      setCollaboratorInfoModalStatus,
      collaboratorInfoModalStatus,
      setSnapInfoModalStatus,
      toggleTheme,
      isSidebarOpen,
      editSection,
      setIsClickedOnMenu,
      setSubscriptionJourneyModal,
      subscriptionStatus,
      callOutData,
    } = this.props;
    const mode = router.query.mode;
    const calloutId = router.query.calloutId;

    // Only log callout-related data when coming from callout route
    if (mode === 'submitSnap' && calloutId) {
      console.log(
        mode,
        'mode===============',
        calloutId,
        'callOutData===============',
        callOutData,
      );
    }
    const { visibleObserver } = this.state;

    const theme = get(projectPreviewData, 'theme', 'dark');
    toggleTheme(theme);

    const onOffCoverStatus = get(
      projectPreviewData,
      'sectionsOnOff.cover',
      false,
    );
    const onOffBasicInfoStatus = get(
      projectPreviewData,
      'sectionsOnOff.basicInfo',
      false,
    );
    const onOffCreativeTeamStatus = get(
      projectPreviewData,
      'sectionsOnOff.creativeTeam',
      false,
    );
    const onOffCastMembersStatus = get(
      projectPreviewData,
      'sectionsOnOff.castMembers',
      false,
    );
    const onOffDiscriptionStatus = get(
      projectPreviewData,
      'sectionsOnOff.projectDisc',
      false,
    );
    const onOffVideoStatus = get(
      projectPreviewData,
      'sectionsOnOff.videos',
      false,
    );
    const onOffBudgetStatus = get(
      projectPreviewData,
      'sectionsOnOff.budget',
      false,
    );
    const onOffFinanceStatus = get(
      projectPreviewData,
      'sectionsOnOff.financePlan',
      false,
    );
    const onOffOtherDocumentsStatus = get(
      projectPreviewData,
      'sectionsOnOff.uploadFileSection',
      false,
    );
    const onOffArtworkStatus = get(
      projectPreviewData,
      'sectionsOnOff.artWork',
      false,
    );
    const onOffPosterStatus = get(
      projectPreviewData,
      'sectionsOnOff.poster',
      false,
    );
    const onOffCompareStatus = get(
      projectPreviewData,
      'sectionsOnOff.comparableProject',
      false,
    );
    const onOffSalesStatus = get(
      projectPreviewData,
      'sectionsOnOff.salesEstimateFile',
      false,
    );

    const isCollaborationLocked =
      !accessibleFeature.projectCollaborators ||
      subscriptionStatus === 'expired';

    const handleCollaboration = () => {
      if (isCollaborationLocked) {
        setSubscriptionJourneyModal({
          modalStatus: true,
          cameFrom: 'projects',
          onComplete: () => handleCollaboration(),
          feature: 'projectCollaborators',
        });
      } else {
        setSubscriptionJourneyModal({
          modalStatus: false,
          feature: '',
          cameFrom: 'projects',
          onComplete: () => {},
        });
        setCollaboratorInfoModalStatus(false);
      }
    };

    return (
      <>
        {isLoading ? (
          <Loader />
        ) : (
          <>
            <ObserverComponent
              refMap={this.refMap}
              visibleObserverSection={this.visibleObserverSection}
            />
            <Modal
              modalShow={collaboratorInfoModalStatus}
              className={`${Style.modalBody}`}
              titleClass={`${Style.modalPublishTitle}`}
              title="Start collaborating"
              isCollaborationLocked={isCollaborationLocked}
              body={this.body()}
              closeCallback={() => {
                !isCollaborationLocked
                  ? setCollaboratorInfoModalStatus(false)
                  : router.push('/projects');
              }}
              isShowCrossBtn
              closeBtnText="cancel"
              successCallback={() => handleCollaboration()}
              successBtnText="continue"
              closeBtnClass="--secondaryChaney"
              successBtnClass="--primaryNavy"
            />
            <div
              className="col-12 d-block d-sm-block d-md-none fixed-top"
              style={{ backgroundColor: '#ffffff' }}
            >
              <div className="mt-3">
                <MobileBar
                  projectPreviewData={projectPreviewData}
                  shareScreen={false}
                  router={router}
                  notificationsList={notificationsList}
                />
              </div>
            </div>
            <div className="row m-0">
              <div
                className={`position-fixed p-0 d-none d-sm-none d-md-block ${Style.leftSide} `}
                style={{ width: isSidebarOpen ? '400px' : '74px' }}
              >
                {get(coverData, 'title') && (
                  <LeftSideBar
                    cover={this.cover}
                    basicInfo={this.basicInfo}
                    creativeTeam={this.creativeTeam}
                    projectDisc={this.projectDisc}
                    videos={this.videos}
                    castMembers={this.castMembers}
                    share={this.share}
                    artWork={this.artWork}
                    financePlan={this.financePlan}
                    budget={this.budget}
                    salesEstimate={this.salesEstimate}
                    uploadFile={this.uploadFile}
                    projectPreviewData={projectPreviewData}
                    shareScreen={false}
                    router={router}
                    comparableProject={this.comparableProject}
                    poster={this.poster}
                    isEditIconShow
                    setVisibleSections={setVisibleSections}
                    footHide={false}
                    notificationsList={notificationsList}
                    updateSectionLocking={updateSectionLocking}
                    startTour={this.startTour}
                    visibleObserver={visibleObserver}
                  />
                )}
              </div>
              {theme === 'bcsTemplate' ||
              theme === 'bcsTemplate2' ||
              theme === 'bcsTemplate3' ? (
                <div
                  className={` p-0 ${isSidebarOpen ? 'openSidebar' : 'collapseSidebar'}`}
                >
                  <div
                    className={`${Style.rightSide} ${isSidebarOpen ? Style.sidebarOpenMargin : Style.sidebarCollapseMargin}`}
                  >
                    <BcsTheme
                      t={t}
                      projectPreviewData={projectPreviewData}
                      router={router}
                      setVisibleSections={setVisibleSections}
                      directorList={directorList}
                      snapStatus={false}
                      setSaveButtonStateArtWork={setSaveButtonStateArtWork}
                      showButtonArtwork={showButtonArtwork}
                      updateProjectArtwork={updateProjectArtwork}
                      removeSectionItems={removeSectionItems}
                      imageList={imageList}
                      userData={userData}
                      writerList={writerList}
                      setImageData={setImageData}
                      updateProjectData={updateProjectData}
                      coverData={coverData}
                      updateCoverInfo={updateCoverInfo}
                      producerList={producerList}
                      isDisable={isDisable}
                      setSaveButtonState={setSaveButtonState}
                      showButton={showButton}
                      deleteFile={deleteFile}
                      visibleSections={visibleSections}
                      updateCompareProject={updateCompareProject}
                      updateProjectBasicInfo={updateProjectBasicInfo}
                      setSaveButtonStateBasicInfo={setSaveButtonStateBasicInfo}
                      showButtonBasic={showButtonBasic}
                      setCreatorTags={setCreatorTags}
                      tags={tags}
                      getTagData={getTagData}
                      tagList={tagList}
                      setTagData={setTagData}
                      updateCreativeTeam={updateCreativeTeam}
                      updateCrewMembers={updateCrewMembers}
                      updateDescription={updateDescription}
                      setSaveButtonStateDescription={
                        setSaveButtonStateDescription
                      }
                      showButtonDesc={showButtonDesc}
                      deleteDescriptionFile={deleteDescriptionFile}
                      updateVideos={updateVideos}
                      isFormStatus={isFormStatus}
                      isOpenForm={isOpenForm}
                      setEditSection={setEditSection}
                      updateFinance={updateFinance}
                      setFinanceEditId={setFinanceEditId}
                      financeEditId={financeEditId}
                      reset={reset}
                      isBudgetOverflow={isBudgetOverflow}
                      isBudgetModalOpen={isBudgetModalOpen}
                      updateBudget={updateBudget}
                      getHistory={getHistory}
                      historyList={historyList}
                      updateSalesEstimation={updateSalesEstimation}
                      setSalesNewSnapStatus={setSalesNewSnapStatus}
                      salesNewSnapRequest={salesNewSnapRequest}
                      setSnapSteps={setSnapSteps}
                      snapStepsStatus={snapStepsStatus}
                      shareProject={shareProject}
                      shareData={shareData}
                      isGenerateQr={isGenerateQr}
                      closeSnapStepsModal={closeSnapStepsModal}
                      loadData={loadData}
                      snapRequest={snapRequest}
                      setSnapRequest={setSnapRequest}
                      updateOtherDocuments={updateOtherDocuments}
                      docsList={docsList}
                      setDocsData={setDocsData}
                      coverImageUrl={coverImageUrl}
                      setCoverImageUrl={setCoverImageUrl}
                      updateProjectPosters={updateProjectPosters}
                      updateUnestimatedBudget={updateUnestimatedBudget}
                      onOffCoverStatus={onOffCoverStatus}
                      onOffBasicInfoStatus={onOffBasicInfoStatus}
                      onOffCreativeTeamStatus={onOffCreativeTeamStatus}
                      onOffCastMembersStatus={onOffCastMembersStatus}
                      onOffDiscriptionStatus={onOffDiscriptionStatus}
                      onOffArtworkStatus={onOffArtworkStatus}
                      onOffVideoStatus={onOffVideoStatus}
                      onOffBudgetStatus={onOffBudgetStatus}
                      onOffFinanceStatus={onOffFinanceStatus}
                      onOffPosterStatus={onOffPosterStatus}
                      onOffOtherDocumentsStatus={onOffOtherDocumentsStatus}
                      onOffCompareStatus={onOffCompareStatus}
                      onOffSalesStatus={onOffSalesStatus}
                      setCoverImage={setCoverImage}
                      coverImg={coverImg}
                      setCoverImageUploadStatus={setCoverImageUploadStatus}
                      coverImgUploadStatus={coverImgUploadStatus}
                      setSnapInfoModalStatus={setSnapInfoModalStatus}
                      coverRef={this.cover}
                      basicInfoRef={this.basicInfo}
                      creativeTeamRef={this.creativeTeam}
                      projectDiscRef={this.projectDisc}
                      videosRef={this.videos}
                      artWorkRef={this.artWork}
                      castMembersRef={this.castMembers}
                      financePlanRef={this.financePlan}
                      budgetRef={this.budget}
                      salesEstimateRef={this.salesEstimate}
                      uploadFileRef={this.uploadFile}
                      posterRef={this.poster}
                      comparableProjectRef={this.comparableProject}
                    />
                  </div>
                </div>
              ) : theme === 'newTemplate' ? (
                <div
                  className={` p-0 ${isSidebarOpen ? 'openSidebar' : 'collapseSidebar'}`}
                >
                  <div
                    className={`${Style.rightSide} ${isSidebarOpen ? Style.sidebarOpenMargin : Style.sidebarCollapseMargin}`}
                  >
                    <CascadeTemplate
                      coverRef={this.cover}
                      basicInfoRef={this.basicInfo}
                      creativeTeamRef={this.creativeTeam}
                      projectDiscRef={this.projectDisc}
                      videosRef={this.videos}
                      artWorkRef={this.artWork}
                      castMembersRef={this.castMembers}
                      financePlanRef={this.financePlan}
                      budgetRef={this.budget}
                      salesEstimateRef={this.salesEstimate}
                      posterRef={this.poster}
                      compareRef={this.comparableProject}
                      otherDocRef={this.uploadFile}
                      setVisibleSections={setVisibleSections}
                      snapStatus={false}
                      isDisable={isDisable}
                      setSaveButtonState={setSaveButtonState}
                      showButton={showButton}
                      deleteFile={deleteFile}
                      visibleSections={visibleSections}
                      isFormStatus={isFormStatus}
                      isOpenForm={isOpenForm}
                      setEditSection={setEditSection}
                      updateSectionLocking={updateSectionLocking}
                      onOffCoverStatus={onOffCoverStatus}
                      onOffBasicInfoStatus={onOffBasicInfoStatus}
                      onOffCreativeTeamStatus={onOffCreativeTeamStatus}
                      onOffCastMembersStatus={onOffCastMembersStatus}
                      onOffDiscriptionStatus={onOffDiscriptionStatus}
                      onOffArtworkStatus={onOffArtworkStatus}
                      onOffVideoStatus={onOffVideoStatus}
                      onOffBudgetStatus={onOffBudgetStatus}
                      onOffFinanceStatus={onOffFinanceStatus}
                      onOffPosterStatus={onOffPosterStatus}
                      onOffOtherDocumentsStatus={onOffOtherDocumentsStatus}
                      onOffCompareStatus={onOffCompareStatus}
                      onOffSalesStatus={onOffSalesStatus}
                      editSection={editSection}
                      setClickedItemOfSidebar={setClickedItemOfSidebar}
                      setIsClickedOnMenu={setIsClickedOnMenu}
                    />
                  </div>
                </div>
              ) : (
                <div
                  className={`themeContainer p-0 ${isSidebarOpen ? 'openSidebar' : 'collapseSidebar'}`}
                >
                  <div
                    className={`${Style.rightSide} ${isSidebarOpen ? Style.sidebarOpenMargin : Style.sidebarCollapseMargin}  px-76`}
                  >
                    <div
                      className={`${
                        visibleSections.includes('cover')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div className="m0Auto " ref={this.cover}>
                        <Cover
                          directorList={directorList}
                          writerList={writerList}
                          projectPreviewData={projectPreviewData}
                          router={router}
                          showEdit="true"
                          updateProjectData={updateProjectData}
                          coverData={coverData}
                          updateCoverInfo={updateCoverInfo}
                          producerList={producerList}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          showButton={showButton}
                          deleteFile={deleteFile}
                          snapStatus={false}
                          onOffCoverStatus={onOffCoverStatus}
                          setCoverImage={setCoverImage}
                          coverImg={coverImg}
                          setCoverImageUploadStatus={setCoverImageUploadStatus}
                          coverImgUploadStatus={coverImgUploadStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('basicInfo')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div ref={this.basicInfo}>
                        <BasicInfo
                          t={t}
                          updateProjectBasicInfo={updateProjectBasicInfo}
                          projectPreviewData={projectPreviewData}
                          setSaveButtonStateBasicInfo={
                            setSaveButtonStateBasicInfo
                          }
                          showButtonBasic={showButtonBasic}
                          router={router}
                          isDisable={isDisable}
                          snapStatus={false}
                          setCreatorTags={setCreatorTags}
                          tags={tags}
                          getTagData={getTagData}
                          tagList={tagList}
                          logLineTextDataStatus={true}
                          logLineTagsStatus={true}
                          formatStatus={true}
                          genreStatus={true}
                          statusFieldStatus={true}
                          runningTimeStatus={true}
                          settingStatus={true}
                          setTagData={setTagData}
                          logLineProjectStatus={true}
                          onOffBasicInfoStatus={onOffBasicInfoStatus}
                        />
                      </div>
                    </div>

                    <div
                      className={`${
                        visibleSections.includes('creativeTeam')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.creativeTeam}
                      >
                        <Team
                          t={t}
                          updateCreativeTeam={updateCreativeTeam}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          snapStatus={false}
                          onOffCreativeTeamStatus={onOffCreativeTeamStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('projectDisc')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.projectDisc}
                      >
                        <Description
                          t={t}
                          updateDescription={updateDescription}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonStateDescription={
                            setSaveButtonStateDescription
                          }
                          showButtonDesc={showButtonDesc}
                          snapStatus={false}
                          deleteDescriptionFile={deleteDescriptionFile}
                          visionDataStatus={true}
                          synopsisDataStatus={true}
                          treatmentDataStatus={true}
                          scriptDataStatus={true}
                          onOffDiscriptionStatus={onOffDiscriptionStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('castMembers')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.castMembers}
                      >
                        <CastMembers
                          t={t}
                          updateCrewMembers={updateCrewMembers}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          snapStatus={false}
                          onOffCastMembersStatus={onOffCastMembersStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('poster')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div className={`${Style.mainRow}`} ref={this.poster}>
                        <Poster
                          updateProjectPosters={updateProjectPosters}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          snapStatus={false}
                          t={t}
                          onOffPosterStatus={onOffPosterStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('artWork')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div className={`${Style.mainRow}`} ref={this.artWork}>
                        <ProjectArtwork
                          t={t}
                          isDisable={isDisable}
                          setSaveButtonStateArtWork={setSaveButtonStateArtWork}
                          showButtonArtwork={showButtonArtwork}
                          updateProjectArtwork={updateProjectArtwork}
                          projectPreviewData={projectPreviewData}
                          router={router}
                          removeSectionItems={removeSectionItems}
                          setImageData={setImageData}
                          imageList={imageList}
                          updateCoverInfo={updateCoverInfo}
                          snapStatus={false}
                          userData={userData}
                          onOffArtworkStatus={onOffArtworkStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('videos')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div className={`${Style.mainRow}`} ref={this.videos}>
                        <Video
                          t={t}
                          updateVideos={updateVideos}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          isFormStatus={isFormStatus}
                          isOpenForm={isOpenForm}
                          removeSectionItems={removeSectionItems}
                          snapStatus={false}
                          setEditSection={setEditSection}
                          onOffVideoStatus={onOffVideoStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('comparableProject')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.comparableProject}
                      >
                        <CompareProject
                          updateCompareProject={updateCompareProject}
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          snapStatus={false}
                          t={t}
                          onOffCompareStatus={onOffCompareStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('financePlan')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.financePlan}
                      >
                        <FinancePlans
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          updateFinance={updateFinance}
                          snapStatus={false}
                          t={t}
                          setFinanceEditId={setFinanceEditId}
                          financeEditId={financeEditId}
                          removeSectionItems={removeSectionItems}
                          setEditSection={setEditSection}
                          reset={reset}
                          onOffFinanceStatus={onOffFinanceStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('budget')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div className={`${Style.mainRow}`} ref={this.budget}>
                        <Budget
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          updateFinance={updateBudget}
                          snapStatus={false}
                          isBudgetOverflow={isBudgetOverflow}
                          isBudgetModalOpen={isBudgetModalOpen}
                          t={t}
                          updateUnestimatedBudget={updateUnestimatedBudget}
                          onOffBudgetStatus={onOffBudgetStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('uploadFileSection')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow} ${Style.lastContainer}`}
                        ref={this.uploadFile}
                      >
                        <UploadFile
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          getHistory={getHistory}
                          snapStatus={false}
                          historyList={historyList}
                          removeSectionItems={removeSectionItems}
                          updateSalesEstimation={updateSalesEstimation}
                          t={t}
                          setEditSection={setEditSection}
                          updateOtherDocuments={updateOtherDocuments}
                          docsList={docsList}
                          setDocsData={setDocsData}
                          onOffOtherDocumentsStatus={onOffOtherDocumentsStatus}
                        />
                      </div>
                    </div>
                    <div
                      className={`${
                        visibleSections.includes('salesEstimateFile')
                          ? ''
                          : Style.shadowDisable
                      } `}
                    >
                      <div
                        className={`${Style.mainRow}`}
                        ref={this.salesEstimate}
                      >
                        <SalesEstimate
                          projectPreviewData={projectPreviewData}
                          isDisable={isDisable}
                          setSaveButtonState={setSaveButtonState}
                          getHistory={getHistory}
                          snapStatus={false}
                          historyList={historyList}
                          removeSectionItems={removeSectionItems}
                          updateSalesEstimation={updateSalesEstimation}
                          t={t}
                          setEditSection={setEditSection}
                          setSalesNewSnapStatus={setSalesNewSnapStatus}
                          salesNewSnapRequest={salesNewSnapRequest}
                          setSnapSteps={setSnapSteps}
                          snapStepsStatus={snapStepsStatus}
                          shareProject={shareProject}
                          shareData={shareData}
                          isGenerateQr={isGenerateQr}
                          closeSnapStepsModal={closeSnapStepsModal}
                          loadData={loadData}
                          snapRequest={snapRequest}
                          setSnapRequest={setSnapRequest}
                          onOffSalesStatus={onOffSalesStatus}
                          setSnapInfoModalStatus={setSnapInfoModalStatus}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </>
    );
  }
}

export async function getStaticPaths() {
  return {
    paths: ['/project/overview/homePage'],
    fallback: true,
  };
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale)),
    },
  };
}

const mapStateToProps = (state) => ({
  isLoading: state.project.isLoading,
  projectPreviewData: state.project.projectPreviewData,
  coverData: state.project.coverData,
  producerList: state.project.producerList,
  directorList: state.project.directorList,
  writerList: state.project.writerList,
  userData: state.auth.userData,
  showButton: state.project.showButton,
  isDisable: state.project.isDisable,
  showButtonBasic: state.project.showButtonBasic,
  showButtonDesc: state.project.showButtonDesc,
  showButtonArtwork: state.project.showButtonArtwork,
  imageList: state.project.imageList,
  isOpenForm: state.project.isOpenForm,
  paginationParams: state.project.paginationParams,
  shareData: state.project.shareData,
  loadData: state.project.loadData,
  historyList: state.project.historyList,
  isBudgetModalOpen: state.project.isBudgetModalOpen,
  visibleSections: state.project.visibleSections,
  docsList: state.project.docsList,
  tags: state.project.tags,
  tagList: state.user.tagList,
  salesNewSnapRequest: state.project.salesNewSnapRequest,
  snapStepsStatus: state.project.snapStepsStatus,
  snapRequest: state.project.snapRequest,
  financeEditId: state.project.financeEditId,
  coverImageUrl: state.project.coverImageUrl,
  notificationsList: state.project.notificationsList,
  coverImg: state.project.coverImg,
  coverImgUploadStatus: state.project.coverImgUploadStatus,
  collaboratorInfoModalStatus: state.project.collaboratorInfoModalStatus,
  isSidebarOpen: state.project.isSidebarOpen,
  editSection: state.project.editSection,
  accessibleFeature: state.subscription.accessibleFeature,
  subscriptionStatus: state.subscription.subscriptionStatus,
  collaboratorList: state.project.collaboratorList,
  callOutData: state.callout.callOutData,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setLoadingStatus: (payload) => dispatch(setLoadingStatus(payload)),
    fetchProjectDetailsByID: (payload) =>
      dispatch(fetchProjectDetailsByID(payload)),
    updateProjectData: (value, id) => dispatch(updateProjectData(value, id)),
    updateProjectBasicInfo: (value, id) =>
      dispatch(updateProjectBasicInfo(value, id)),
    setSaveButtonState: (payload) => dispatch(setSaveButtonState(payload)),
    updateCreativeTeam: (value, id, showFormStatus) =>
      dispatch(updateCreativeTeam(value, id, showFormStatus)),
    updateCoverInfo: (value, id, action) =>
      dispatch(updateCoverInfo(value, id, action)),
    updateDescription: (value, id, showFormStatus) =>
      dispatch(updateDescription(value, id, showFormStatus)),
    setSaveButtonStateBasicInfo: (payload) =>
      dispatch(setSaveButtonStateBasicInfo(payload)),
    setSaveButtonStateDescription: (payload) =>
      dispatch(setSaveButtonStateDescription(payload)),
    setSaveButtonStateArtWork: (payload) =>
      dispatch(setSaveButtonStateArtWork(payload)),
    updateProjectArtwork: (action, value, id, type, successMsg) =>
      dispatch(updateProjectArtwork(action, value, id, type, successMsg)),
    setImageData: (payload) => dispatch(setImageData(payload)),
    updateVideos: (value, id, redirect) =>
      dispatch(updateVideos(value, id, redirect)),
    isFormStatus: (status) => dispatch(isFormStatus(status)),
    removeSectionItems: (projectId, section, sectionObjid) =>
      dispatch(removeSectionItems(projectId, section, sectionObjid)),
    updateCrewMembers: (value, id, showFormStatus) =>
      dispatch(updateCrewMembers(value, id, showFormStatus)),
    updateCompareProject: (value, id, showFormStatus) =>
      dispatch(updateCompareProject(value, id, showFormStatus)),
    updateFinance: (value, id, redirect) =>
      dispatch(updateFinance(value, id, redirect)),
    updateBudget: (value, id, redirect) =>
      dispatch(updateBudget(value, id, redirect)),
    deleteFile: (id, section) => dispatch(deleteFile(id, section)),
    isBudgetOverflow: (status) => dispatch(isBudgetOverflow(status)),
    setVisibleSections: (payload) => dispatch(setVisibleSections(payload)),
    getHistory: (payload) => dispatch(getHistory(payload)),
    updateSalesEstimation: (value, id, section) =>
      dispatch(updateSalesEstimation(value, id, section)),
    setTheme: (payload) => dispatch(setTheme(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    deleteDescriptionFile: (id, type) =>
      dispatch(deleteDescriptionFile(id, type)),
    setEditSection: (status) => dispatch(setEditSection(status)),
    updateOtherDocuments: (value, id, section) =>
      dispatch(updateOtherDocuments(value, id, section)),
    setDocsData: (payload) => dispatch(setDocsData(payload)),
    setCreatorTags: (payload) => dispatch(setCreatorTags(payload)),
    getTagData: (payload) => dispatch(getTagData(payload)),
    setTagData: (payload) => dispatch(setTagData(payload)),
    setSalesNewSnapStatus: (payload) =>
      dispatch(setSalesNewSnapStatus(payload)),
    setSnapSteps: (payload) => dispatch(setSnapSteps(payload)),
    shareProject: (id, value) => dispatch(shareProject(id, value)),
    isGenerateQr: (note) => dispatch(isGenerateQr(note)),
    closeSnapStepsModal: () => dispatch(closeSnapStepsModal()),
    setSnapRequest: (payload) => dispatch(setSnapRequest(payload)),
    setFinanceEditId: (id) => dispatch(setFinanceEditId(id)),
    reset: () => dispatch(reset('financeForm')),
    setCoverImageUrl: (url) => dispatch(setCoverImageUrl(url)),
    fetchDmProjects: (payload) => dispatch(fetchDmProjects(payload)),
    setCollaboratorProjectId: (payload) =>
      dispatch(setCollaboratorProjectId(payload)),
    updateSectionLocking: (id, action, payload) =>
      dispatch(updateSectionLocking(id, action, payload)),
    setProjectPreviewData: (payload) =>
      dispatch(setProjectPreviewData(payload)),
    updateProjectPosters: (value, id, showFormStatus) =>
      dispatch(updateProjectPosters(value, id, showFormStatus)),
    updateUnestimatedBudget: (payload, id, section) =>
      dispatch(updateUnestimatedBudget(payload, id, section)),
    setCoverImage: (payload) => dispatch(setCoverImage(payload)),
    setCoverImageUploadStatus: (payload) =>
      dispatch(setCoverImageUploadStatus(payload)),
    setCollaboratorInfoModalStatus: (status) =>
      dispatch(setCollaboratorInfoModalStatus(status)),
    setActiveGoals: (payload) => dispatch(setActiveGoals(payload)),
    updateGoals: (goal, id) => dispatch(updateGoals(goal, id)),
    setSnapInfoModalStatus: (payload) =>
      dispatch(setSnapInfoModalStatus(payload)),
    setSidebarStatus: (payload) => dispatch(setSidebarStatus(payload)),
    setLeftSideBarActiveTab: (payload) =>
      dispatch(setLeftSideBarActiveTab(payload)),
    setClickedItemOfSidebar: (payload) =>
      dispatch(setClickedItemOfSidebar(payload)),
    setIsClickedOnMenu: (payload) => dispatch(setIsClickedOnMenu(payload)),
    setSubscriptionJourneyModal: (payload) =>
      dispatch(setSubscriptionJourneyModal(payload)),
    fetchCollaboratorList: (payload) =>
      dispatch(fetchCollaboratorList(payload)),
    getCallOut: (id) => dispatch(getCallOut(id)),
  };
};

HomePage.defaultProps = {};

HomePage.propTypes = {
  updateProjectData: PropTypes.func.isRequired,
  isLoading: PropTypes.bool.isRequired,
  projectPreviewData: PropTypes.func.isRequired,
  router: PropTypes.object.isRequired,
  fetchProjectDetailsByID: PropTypes.func.isRequired,
  updateProjectBasicInfo: PropTypes.func.isRequired,
  coverData: PropTypes.bool.isRequired,
  updateCoverInfo: PropTypes.func.isRequired,
  updateCreativeTeam: PropTypes.func.isRequired,
  updateDescription: PropTypes.func.isRequired,
  producerList: PropTypes.array.isRequired,
  writerList: PropTypes.array.isRequired,
  directorList: PropTypes.array.isRequired,
  userData: PropTypes.func.isRequired,
  setSaveButtonState: PropTypes.func.isRequired,
  showButton: PropTypes.func.isRequired,
  showButtonBasic: PropTypes.func.isRequired,
  showButtonDesc: PropTypes.func.isRequired,
  isDisable: PropTypes.func.isRequired,
  showButtonArtwork: PropTypes.func.isRequired,
  setSaveButtonStateBasicInfo: PropTypes.func.isRequired,
  setSaveButtonStateDescription: PropTypes.func.isRequired,
  setSaveButtonStateArtWork: PropTypes.func.isRequired,
  updateProjectArtwork: PropTypes.func.isRequired,
  setImageData: PropTypes.func.isRequired,
  imageList: PropTypes.func.isRequired,
  updateVideos: PropTypes.func.isRequired,
  isFormStatus: PropTypes.bool.isRequired,
  isOpenForm: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  updateOtherDocuments: PropTypes.func.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
  updateCrewMembers: PropTypes.func.isRequired,
  updateCompareProject: PropTypes.func.isRequired,
  updateFinance: PropTypes.func.isRequired,
  deleteFile: PropTypes.func.isRequired,
  isBudgetOverflow: PropTypes.func.isRequired,
  isBudgetModalOpen: PropTypes.bool.isRequired,
  setVisibleSections: PropTypes.func.isRequired,
  visibleSections: PropTypes.array.isRequired,
  docsList: PropTypes.array.isRequired,
  getHistory: PropTypes.func.isRequired,
  updateSalesEstimation: PropTypes.func.isRequired,
  historyList: PropTypes.array.isRequired,
  deleteDescriptionFile: PropTypes.func.isRequired,
  setEditSection: PropTypes.func.isRequired,
  setDocsData: PropTypes.func.isRequired,
  setCreatorTags: PropTypes.func.isRequired,
  tags: PropTypes.array.isRequired,
  getTagData: PropTypes.func.isRequired,
  tagList: PropTypes.array.isRequired,
  setTagData: PropTypes.func.isRequired,
  setSalesNewSnapStatus: PropTypes.func.isRequired,
  snapStepsStatus: PropTypes.object.isRequired,
  setSnapSteps: PropTypes.func.isRequired,
  shareProject: PropTypes.func.isRequired,
  shareData: PropTypes.object.isRequired,
  isGenerateQr: PropTypes.func.isRequired,
  closeSnapStepsModal: PropTypes.func.isRequired,
  loadData: PropTypes.object.isRequired,
  snapRequest: PropTypes.bool.isRequired,
  setSnapRequest: PropTypes.func.isRequired,
  salesNewSnapRequest: PropTypes.bool.isRequired,
  setFinanceEditId: PropTypes.func.isRequired,
  financeEditId: PropTypes.string.isRequired,
  reset: PropTypes.func.isRequired,
  updateBudget: PropTypes.func.isRequired,
  coverImageUrl: PropTypes.string.isRequired,
  setCoverImageUrl: PropTypes.func.isRequired,
  notificationsList: PropTypes.array.isRequired,
  fetchDmProjects: PropTypes.func.isRequired,
  setCollaboratorProjectId: PropTypes.func.isRequired,
  updateSectionLocking: PropTypes.func.isRequired,
  setProjectPreviewData: PropTypes.func.isRequired,
  updateProjectPosters: PropTypes.func.isRequired,
  updateUnestimatedBudget: PropTypes.func.isRequired,
  setCoverImage: PropTypes.func.isRequired,
  coverImg: PropTypes.string.isRequired,
  setCoverImageUploadStatus: PropTypes.func.isRequired,
  coverImgUploadStatus: PropTypes.bool.isRequired,
  setCollaboratorInfoModalStatus: PropTypes.func.isRequired,
  collaboratorInfoModalStatus: PropTypes.bool.isRequired,
  setActiveGoals: PropTypes.func.isRequired,
  updateGoals: PropTypes.func.isRequired,
  setSnapInfoModalStatus: PropTypes.func.isRequired,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withRouter(withTranslation('common')(HomePage)));
