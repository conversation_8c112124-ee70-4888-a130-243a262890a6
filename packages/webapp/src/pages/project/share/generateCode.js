import React, { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/router';
import { reduxForm } from 'redux-form';
import Router from 'next/router';
import Button from 'sharedComponents/Button/button';
import { get } from 'lodash';
import { withTranslation } from 'react-i18next';
import { pushToDataLayer } from 'lib/commonUtil';
import PropTypes from 'prop-types';
import Modal from 'sharedComponents/Modal/shareModal';
import ShareModal from './shareSnaps';
import Style from './style/share.module.scss';
import Styles from './style/multiStepHeading.module.scss';
import mixpanelAnalytics from 'lib/mixpanel';

const ShareFirstStep = ({
  loadData,
  cancle,
  t,
  shareData,
  salesNewSnapRequest,
  updateSalesEstimation,
  setSnapRequest,
  snapStepsStatus,
  handleChangePublishSnap,
  initialize,
  currentFlow,
  addSubmissionToCallout,
  callout,
  setSnapSteps,
}) => {
  const [btnText, setBtnText] = useState('COPY URL');
  const router = useRouter();
  const [showCopyLinkModal, setShowCopyLinkModal] = useState(false);
  const modalStatus = get(snapStepsStatus, 'generateQr', false);
  const [successModal, setSuccessModal] = useState(false);
  const [showModal, setShowModal] = useState(true);
  const [snapMessage] =
    useState(`Here’s a link to view my Project on My SMASH Media. I’m using My SMASH Media because it has free IP protection, this protects both the creator and the recipient.

To see my pitch, please create a free My SMASH Media account, it’s easy and you’ll only see the projects you want to see.

Thanks for being part of the creative revolution.
        
What is My SMASH Media?
My SMASH Media is a suite of digital tools to help us all create and discover professional pitch decks for Film and TV. For more information go to MYSMASH.MEDIA https://www.mysmash.media or watch this 90 secs video https://bit.ly/3mZKjKA.`);

  const initializeForm = useCallback(
    async (hash) => {
      const formValues = {
        copyMsg: snapMessage,
        link: `${process.env.SnapBaseUrl}project/snap/${hash}`,
      };

      await initialize(formValues);
    },
    [initialize, snapMessage],
  );

  useEffect(() => {
    return () => {
      setShowModal(modalStatus);
    };
  }, []);

  const closeCopyLinkModal = () => {
    setShowCopyLinkModal(false);
  };

  const toCopyUrl = () => {
    const hash = get(shareData, 'hash', '');
    setShowCopyLinkModal(true);
    initializeForm(hash);
    navigator.clipboard.writeText(
      `${process.env.SnapBaseUrl}project/snap/${hash}`,
    );
    pushToDataLayer({
      action: 'snapshot url copied',
      category: 'Snapshot',
      label: 'Snapshot url copied successfully',
    });
    mixpanelAnalytics('snapshot_share_link', {
      pageName: 'profile_share',
      snapshotUrl: `${process.env.SnapBaseUrl}project/snap/${hash}`,
    });
    setBtnText('Copied!');
  };

  const goToSnap = () => {
    const hash = get(shareData, 'hash', '');
    if (hash) {
      window.open(`/project/snap/${hash}`, '_blank');
      mixpanelAnalytics('snapshot_view', {
        pageName: 'profile_share',
        redirectUrl: `/project/snap/${hash}`,
      });
    }
  };

  const goToCallouts = () => {
    router.push('/callouts');
  };

  //handle add submission
  const addSubmission = () => {
    addSubmissionToCallout(get(callout, '_id', ''), {
      id: get(shareData, '_id', ''),
      body: get(shareData, 'body', ''),
    }).then(() => {
      // Redirect to callouts project list instead of showing success modal
      router.push('/callouts/projectsList');
    });
  };

  const requestedSnapForSales = async () => {
    const SnapId = get(shareData, '_id', false);
    const projectId = get(shareData, 'projectId', false);
    const formData = { snapRequestId: SnapId };
    if (SnapId && projectId) {
      await updateSalesEstimation(formData, projectId, 'salesEstimateSnap');
      await cancle();
      await setSnapRequest(true);
    }
  };

  const successModalBody = () => {
    return (
      <div className={Style.shareContainer}>
        <div className={`${Styles.stepContainer} col-12`}>
          <div className={`${Styles.step} col-4`}>
            <div className={`${Styles.stepNumber} ${Style.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>1</p>
            </div>
            <div>
              <p
                className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
              >
                {t('common:projects.overview.share.gettingReady')}
              </p>
            </div>
            <div className={Styles.hrLine} />
          </div>
          <div className={`${Styles.step} col-5`}>
            <div className={`${Styles.stepNumber} ${Styles.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>2</p>
            </div>
            <p
              className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
            >
              {t('common:projects.overview.share.note')}
            </p>
            <div className={Styles.hrLine} />
          </div>
          <div
            className={`${Styles.step} ${currentFlow === 'signUpFlow' ? '' : 'col-3'}`}
          >
            <div className={`${Styles.stepNumber} ${Styles.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>3</p>
            </div>
            <p
              className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
            >
              {currentFlow === 'signUpFlow'
                ? t('common:projects.overview.share.callOutUnique')
                : t('common:projects.overview.share.unique')}
            </p>
          </div>
        </div>
        <div className={Styles.brLine} />
        <div
          className="col-12 p-5 align-content-around"
          style={{ minHeight: '214px' }}
        >
          <div className="mt-3">
            <h4>Snapshot submitted successfully</h4>
          </div>
          <div className="col-12 my-4">
            <p className="p2 px-5 text-center">
              Your snapshot has been successfully submitted to the call out. You
              will be notified when you receive feedback or the status of your
              submission changes..
            </p>
          </div>
          <div
            className="col-12 d-flex justify-content-center"
            style={{ gap: '16px' }}
          >
            <button
              type="button"
              className={`${Style.newSnapBtn} text-white`}
              onClick={goToCallouts}
            >
              View more call outs
            </button>
          </div>
        </div>
      </div>
    );
  };

  const modalBody = () => {
    const SnapId = get(shareData, '_id', '');
    return (
      <div className={Style.shareContainer}>
        <div className={`${Styles.stepContainer} col-12`}>
          <div className={`${Styles.step} col-4`}>
            <div className={`${Styles.stepNumber} ${Style.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>1</p>
            </div>
            <div>
              <p
                className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
              >
                {t('common:projects.overview.share.gettingReady')}
              </p>
            </div>
            <div className={Styles.hrLine} />
          </div>
          <div className={`${Styles.step} col-5`}>
            <div className={`${Styles.stepNumber} ${Styles.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>2</p>
            </div>
            <p
              className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
            >
              {t('common:projects.overview.share.note')}
            </p>
            <div className={Styles.hrLine} />
          </div>
          <div
            className={`${Styles.step} ${currentFlow === 'signUpFlow' ? '' : 'col-3'}`}
          >
            <div className={`${Styles.stepNumber} ${Styles.stepBgActive}`}>
              <p className={`${Styles.stepText} fs-16`}>3</p>
            </div>
            <p
              className={`${Styles.stepLabel} ${Styles.stepActiveLabel} fs-16`}
            >
              {currentFlow === 'signUpFlow'
                ? t('common:projects.overview.share.callOutUnique')
                : t('common:projects.overview.share.unique')}
            </p>
          </div>
        </div>
        <div className={Styles.brLine} />
        <div className="row m-0">
          {loadData ? (
            <div
              className="col-12 d-flex justify-content-center align-items-center"
              style={{ minHeight: '300px' }}
            >
              <p className="text-primary text-center">
                {t('common:projects.overview.share.attached')}
              </p>
            </div>
          ) : (
            <>
              <div
                className="col-12 p-5 align-content-around"
                style={{ minHeight: '214px' }}
              >
                {currentFlow === 'signUpFlow' ? (
                  <>
                    <div className="mt-3">
                      <h4>
                        {t('common:projects.overview.share.callOutHeading')}
                      </h4>
                    </div>
                    <div className="col-12 my-4">
                      <p className="p2 px-5 text-center">
                        Your project snapshot is ready to be submitted.
                      </p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="mt-3">
                      <h4>
                        {t('common:projects.overview.share.decsShareInfo')}
                      </h4>
                    </div>
                    <div className="col-12 my-4">
                      <p className="p2 px-5 text-center">
                        You have successfully created a snapshot of your project
                        that can be published in the Smash discovery section,
                        shared externally as a link or that you can submit to a
                        call out.
                      </p>
                    </div>
                  </>
                )}
                {!salesNewSnapRequest ? (
                  <div
                    className="col-12 d-flex justify-content-center"
                    style={{ gap: '16px' }}
                  >
                    {currentFlow === 'signUpFlow' ? (
                      <>
                        <button
                          type="button"
                          className={`${Style.newSnapBtn} text-white`}
                          onClick={addSubmission}
                        >
                          Submit to a call out
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          type="button"
                          className={`${Style.newSnapBtn} text-white`}
                          onClick={() => goToSnap()}
                        >
                          View
                        </button>
                        <button
                          type="button"
                          className={`${Style.newSnapBtn} text-white`}
                          onClick={() => handleChangePublishSnap(SnapId)}
                        >
                          Publish
                        </button>
                        <button
                          type="button"
                          className={`${Style.newSnapBtn} text-white`}
                          onClick={() => toCopyUrl()}
                        >
                          Share
                        </button>
                        <button
                          type="button"
                          className={`${Style.newSnapBtn} text-white`}
                          onClick={() => {
                            setSnapSteps({ generateQr: false });
                            Router.push({
                              pathname: '/callouts',
                              query: { snap: SnapId },
                            });
                            mixpanelAnalytics('view_callouts', {
                              pageName: 'profile_share',
                              redirectUrl: '/callouts',
                            });
                          }}
                        >
                          Submit to a call out
                        </button>
                      </>
                    )}
                  </div>
                ) : (
                  <div className="col-12 d-none d-sm-none d-md-block">
                    <div className="d-flex justify-content-center">
                      <div className="px-4 mb-4">
                        <Button
                          btntype="button"
                          size="medium"
                          customClass="--primaryNavy"
                          clickHandler={requestedSnapForSales}
                          buttonValue="REQUEST SALES ESTIMATE"
                        />
                      </div>
                      <div>
                        <Button
                          btntype="button"
                          size="medium"
                          customClass="--primaryNavy"
                          clickHandler={cancle}
                          buttonValue="CANCEL"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div className="col-10 d-block d-sm-block d-md-none m0Auto">
                <div className="row">
                  <div className="col-6 mt-4">
                    <Button
                      btntype="button"
                      size="large"
                      customClass="--primaryNavy"
                      clickHandler={goToCallouts}
                      buttonValue={btnText}
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  const snapHash = get(shareData, 'hash', '');

  return (
    <>
      <Modal
        modalShow={showModal}
        body={successModal ? successModalBody() : modalBody()}
        className={Style.snapStepsModalBody}
        closeCallback={cancle}
        modalSize="xl"
        titleClass={Style.modalPublishTitle}
      />
      <ShareModal
        showCopyLinkModal={showCopyLinkModal}
        hash={snapHash}
        closeCopyModal={closeCopyLinkModal}
      />
    </>
  );
};

ShareFirstStep.propTypes = {
  loadData: PropTypes.bool.isRequired,
  cancle: PropTypes.func.isRequired,
  t: PropTypes.func.isRequired,
  shareData: PropTypes.object.isRequired,
  salesNewSnapRequest: PropTypes.bool.isRequired,
  updateSalesEstimation: PropTypes.func.isRequired,
  setSnapRequest: PropTypes.func.isRequired,
  snapStepsStatus: PropTypes.object.isRequired,
  handleChangePublishSnap: PropTypes.func.isRequired,
  initialize: PropTypes.func.isRequired,
};

export default reduxForm({
  form: 'copyLinkForm',
})(withTranslation('common')(ShareFirstStep));
