/* eslint-disable @next/next/no-img-element */
import React, { useState } from 'react';
import { get } from 'lodash';
import Router from 'next/router';
import { withTranslation } from 'react-i18next';
import Style from 'pages/projects/styles/startApp.module.scss';
import Icon from 'sharedComponents/Icon/Icon';
import CrossSvg from 'svgpath/CrossIconSvgPath';
import SnapsView from './snapsView';
import PlusIconSvgPath from 'svgpath/PlushButtonSvgPath';
// import LockIcon from 'sharedComponents/Icon/LockIcon';
import mixpanelAnalytics from 'lib/mixpanel';
import Overview from 'sharedComponents/Overview';
import Button from 'sharedComponents/Button/button';

function Projects(props) {
  const {
    projectsList,
    callout,
    selectProject,
    historyList,
    getProjectSnapshots,
    addSubmissionToCallout,
    accessibleFeature,
    subscriptionStatus,
    setSubscriptionJourneyModal,
    allProjectCount,
  } = props;
  console.log(callout, 'callout==========>');

  const [selectedProjectId, setSelectedProjectId] = useState(null);
  const [selectedSnapId, setSelectedSnapId] = useState(null);
  const [selectedSnapBody, setSelectedSnapBody] = useState(null);

  const list = projectsList || [];

  const handleProjectClick = (id) => {
    setSelectedProjectId(id);
    selectProject(id);
    mixpanelAnalytics('select_project', {
      pageName: 'callouts_projects_lists',
      projectName: get(
        list.find((item) => item._id === id),
        'cover.title',
        '',
      ),
    });
  };

  const createProject = () => {
    if (
      subscriptionStatus !== 'expired' &&
      (accessibleFeature.unlimitedProjects || allProjectCount < 2)
    ) {
      setSubscriptionJourneyModal({
        modalStatus: false,
        feature: '',
        cameFrom: 'projects',
        onComplete: () => {},
      });
      Router.push('/project/create');
    } else {
      setSubscriptionJourneyModal({
        modalStatus: true,
        cameFrom: 'projects',
        onComplete: createProject,
        feature: 'unlimitedProjects',
      });
    }
  };
  const addSubmission = () => {
    addSubmissionToCallout(get(callout, '_id', ''), {
      id: selectedSnapId,
      body: get(selectedSnapBody, 'body', {}),
    }).then(() => {
      const query = {};
      query.snap = get(selectedSnapBody, '_id', '');
      query.calloutId = get(callout, '_id', '');
      Router.push({
        pathname: `/callouts/successSubmission`,
        query: query,
      });
      mixpanelAnalytics('snapshot_submit_to_callout', {
        pageName: 'callout_list',
        calloutName: get(callout, 'name', ''),
        redirectUrl: 'callouts/successSubmission',
      });
    });
  };
  const submissions = get(callout, 'submissions', []);
  const showProject = list
    .filter((item) => !selectedProjectId || item._id === selectedProjectId)
    .map((item) => {
      return (
        <div
          className={`col-12 col-md-6 col-lg-6 mb-0 mb-md-4 mb-lg-4 px-0 p-md-3`}
          key={item._id}
        >
          <div
            className={`${
              Style.existingAppContainer
            } bg-secondary mb-3 mb-md-0 mb-lg-0 p-3 p-md-5 p-lg-5 ${
              selectedProjectId && Style.selectedProject
            }`}
            onClick={() => {
              handleProjectClick(item._id);
            }}
          >
            <div className={`${Style.existingAppSubContainer}`}>
              {selectedProjectId && (
                <div
                  className={`${Style.crossPosition}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedProjectId(null);
                    setSelectedSnapId(null);
                  }}
                >
                  <Icon
                    icon={CrossSvg}
                    color="#00000"
                    viewBox="20"
                    iconSize="20px"
                  />
                </div>
              )}
              <Overview cover={item.cover} item={item} callout />
            </div>
          </div>
        </div>
      );
    });

  const handleCreateNewSnap = () => {
    const calloutId = get(callout, '_id', '');
    Router.push(
      `/project/overview/${selectedProjectId}?mode=submitSnap&calloutId=${calloutId}`,
    );
  };

  return (
    <>
      <div className="container">
        <div className="my-32 my-md-48 my-lg-48 mx-auto">
          <div className="text-center pt-5 pt-md-0 pt-lg-0">
            <div className={`${Style.projects}`}>
              <h1 className="text-center text-primary mb-3 fs-18 fs-md-24 fs-lg-24">
                {get(callout, 'name', '')}
              </h1>
            </div>
            <p
              className="text-primary text-center p2 mx-auto"
              style={{ maxWidth: '700px', lineHeight: '21px' }}
            >
              Select one of your existing projects and snapshots to enter to the
              call out.
            </p>
          </div>

          <h3 className=" ml-3 mt-md-5 mb-3 mb-md-4 mb-lg-4 mt-lg-5 text-center text-md-left text-lg-left fs-16 fs-md-20 fs-lg-20">
            Select Project
          </h3>
          <div className="row m-auto">
            {showProject}
            {!selectedProjectId && (
              <div
                className={`col-12 col-md-6 col-lg-6 mb-4 px-0 p-md-3 d-none d-md-block d-lg-block`}
              >
                {' '}
                <div
                  className={`${Style.newAppContainer} bg-secondary p-3 p-md-5 p-lg-5`}
                  onClick={() => createProject()}
                >
                  <div className={`${Style.newAppSubContainer}`}>
                    <div className={`${Style.plusContainer} mb-3`}>
                      <div
                        data-cy="newProjectContainer"
                        className={`${Style.plusBorder}`}
                      >
                        <Icon
                          icon={PlusIconSvgPath}
                          color="#00000"
                          iconSize="50px"
                        />
                      </div>
                    </div>
                    <div className={`${Style.existingTitle}`}>
                      <p
                        className="p1 text-primary"
                        style={{ cursor: 'pointer' }}
                      >
                        Create New Project
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        {selectedProjectId && (
          <div className="col-12 px-0 px-md-3 px-lg-3">
            <div className="row justify-content-between m-0">
              <h3 className="text-center text-md-left text-lg-left fs-16 fs-md-20 fs-lg-20">
                Select Snapshot
              </h3>
              <Button
                btntype="button"
                customClass="secondary-white-btn"
                className="py-2 px-28 py-md-1 px-md-3"
                clickHandler={handleCreateNewSnap}
                buttonValue="Create new Snapshot"
              />
            </div>
            <SnapsView
              snapsList={historyList}
              projectPreviewData={selectedProjectId}
              getProjectSnapshots={getProjectSnapshots}
              setSelectedSnapId={setSelectedSnapId}
              selectedSnapId={selectedSnapId}
              setSelectedSnapBody={setSelectedSnapBody}
              submissions={submissions}
              callout={callout}
            />
          </div>
        )}
      </div>
      {selectedSnapId && (
        <div className={`footer ${Style.footer} position-relative`}>
          <button
            type="button"
            className="commonBtnClas mr-md-85 mr-0"
            onClick={addSubmission}
          >
            SUBMIT
          </button>
        </div>
      )}
    </>
  );
}

export default withTranslation('common')(Projects);
