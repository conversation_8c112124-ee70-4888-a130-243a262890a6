/* eslint-disable no-useless-escape */
/* eslint-disable no-nested-ternary */
/* eslint-disable default-case */
/* eslint-disable no-unused-vars */
import React from 'react';
import { withTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { get, some, filter, matches } from 'lodash';
import LockIconSvgPath from 'svgpath/LockIconSvgPath';
import Icon from 'sharedComponents/Icon/Icon';
import Style from '../styles/leftSideBar.module.scss';
import InlineSvg from 'sharedComponents/inline-svg';
import { Tooltip, OverlayTrigger } from 'react-bootstrap';

// Project Creation form component
class MenuList extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  // method to enable edit for specific section
  editHandler = () => {
    const {
      editSectionHandler,
      item,
      isClickedEdit,
      alertModalHandler,
      updateSectionLocking,
      projectPreviewData,
      userData,
      leftSideActiveGoals,
    } = this.props;
    const currentUserId = get(userData, '_id');
    if (!isClickedEdit) {
      editSectionHandler(item.section);
      updateSectionLocking(projectPreviewData._id, 'lock', {
        section: item.section,
        userId: currentUserId,
      });
    } else {
      alertModalHandler(true);
    }
  };

  // method to save data for specific section
  saveHandler = () => {
    const {
      saveSectionHandler,
      item,
      updateSectionLocking,
      projectPreviewData,
      userData,
    } = this.props;

    const currentUserId = get(userData, '_id');

    saveSectionHandler(item.section);
    updateSectionLocking(projectPreviewData._id, 'remove', {
      section: item.section,
      userId: currentUserId,
    });
  };

  // method to disable edit for specific section
  cancelHandler = () => {
    const {
      cancelSectionHandler,
      item,
      updateSectionLocking,
      projectPreviewData,
      userData,
    } = this.props;
    const currentUserId = get(userData, '_id');
    cancelSectionHandler(item.section);
    updateSectionLocking(projectPreviewData._id, 'remove', {
      section: item.section,
      userId: currentUserId,
    });
  };

  // method to scroll to specific section
  scrollSection = () => {
    const { scrollSectionHandler, item, isClickedEdit } = this.props;
    if (!isClickedEdit) scrollSectionHandler(item.section);
  };

  // method to link save and cancle button
  linkButtonClick = () => {
    const { linkButtonHandler, item, isClickedEdit, alertModalHandler } =
      this.props;
    if (!isClickedEdit) {
      linkButtonHandler(item.section);
    } else {
      alertModalHandler(true);
    }
  };

  // Set lock modal status.
  lockModalStatus = () => {
    const { toggleSectionLock } = this.props;
    toggleSectionLock();
  };

  // Set section on off status.
  SetSectionOnOffStatus = (section) => {
    const { updateOnOffSections, projectPreviewData, cancelSectionHandler } =
      this.props;
    const projectId = get(projectPreviewData, '_id');
    cancelSectionHandler('');
    const coverStatus = get(
      projectPreviewData,
      'sectionsOnOff.cover',
      'unLock',
    );
    const basicInfoStatus = get(
      projectPreviewData,
      'sectionsOnOff.basicInfo',
      'unLock',
    );
    const creativeTeamStatus = get(
      projectPreviewData,
      'sectionsOnOff.creativeTeam',
      'unLock',
    );
    const castMembersStatus = get(
      projectPreviewData,
      'sectionsOnOff.castMembers',
      'unLock',
    );
    const discriptionStatus = get(
      projectPreviewData,
      'sectionsOnOff.projectDisc',
      'unLock',
    );
    const videoStatus = get(
      projectPreviewData,
      'sectionsOnOff.videos',
      'unLock',
    );
    const budgetStatus = get(
      projectPreviewData,
      'sectionsOnOff.budget',
      'unLock',
    );
    const financeStatus = get(
      projectPreviewData,
      'sectionsOnOff.financePlan',
      'unLock',
    );
    const otherDocumentsStatus = get(
      projectPreviewData,
      'sectionsOnOff.uploadFileSection',
      'unLock',
    );
    const artworkStatus = get(
      projectPreviewData,
      'sectionsOnOff.artWork',
      'unLock',
    );
    const posterStatus = get(
      projectPreviewData,
      'sectionsOnOff.poster',
      'unLock',
    );
    const compareStatus = get(
      projectPreviewData,
      'sectionsOnOff.comparableProject',
      'unLock',
    );
    const salesStatus = get(
      projectPreviewData,
      'sectionsOnOff.salesEstimateFile',
      'unLock',
    );

    const data = {
      cover: coverStatus,
      basicInfo: basicInfoStatus,
      creativeTeam: creativeTeamStatus,
      castMembers: castMembersStatus,
      projectDisc: discriptionStatus,
      videos: videoStatus,
      budget: budgetStatus,
      financePlan: financeStatus,
      uploadFileSection: otherDocumentsStatus,
      artWork: artworkStatus,
      poster: posterStatus,
      comparableProject: compareStatus,
      salesEstimateFile: salesStatus,
    };
    if (data[section] === 'unLock') {
      data[section] = 'lock';
    } else {
      data[section] = 'unLock';
    }
    updateOnOffSections(data, projectId);
  };

  setsectionIcon = (sectionType) => {
    switch (sectionType) {
      case 'Cover': {
        return 'cover';
      }
      case 'Basic Information': {
        return 'basic';
      }
      case 'Creative Team': {
        return 'creativeTeam';
      }
      case 'Description': {
        return 'description';
      }
      case 'Cast Members': {
        return 'castMemebers';
      }
      case 'Project Posters': {
        return 'projectPosters';
      }
      case 'Mood Board': {
        return 'moodBoard';
      }
      case 'Project Videos': {
        return 'videos';
      }
      case 'Comparable Projects': {
        return 'comparableProjects';
      }
      case 'Finance Plan': {
        return 'financePlan';
      }
      case 'Budget': {
        return 'budget';
      }
      case 'Other Documents': {
        return 'other';
      }
      case 'Sales Estimates': {
        return 'salesEstimate';
      }
      default: {
        return 'other';
      }
    }
  };

  renderTooltip = (props) => (
    <Tooltip id="button-tooltip" {...props}>
      Tooltip on the right
    </Tooltip>
  );

  render() {
    const {
      item,
      clickedItem,
      isClickedEdit,
      isEditMode,
      isEditIconShow,
      projectPreviewData,
      userData,
      isSidebarOpen,
      leftSideActiveGoals,
      visibleObserver,
      anyEditTrue,
    } = this.props;

    const popperConfig = {
      modifiers: [
        {
          name: 'offset',
          options: {
            offset: [0, 40],
          },
        },
      ],
    };

    const sectionsGoal = get(item, 'goals', '');
    const sectionsData = get(projectPreviewData, 'sectionsOnOff', {});
    const currentUserId = get(userData, '_id');
    const sectionsAlreadyLock = get(projectPreviewData, 'editSections', []);
    const disable =
      (isClickedEdit && !isEditMode) || sectionsData[item.section] === 'lock'
        ? `${Style.disable}`
        : '';
    let locked = false;
    const lockedSections = filter(
      sectionsAlreadyLock,
      matches({
        section: item.section,
      }),
    );

    if (lockedSections.length > 0) {
      const currentUrerlocked = !some(sectionsAlreadyLock, {
        section: item.section,
        userId: currentUserId,
      });
      if (currentUrerlocked) {
        locked = true;
      } else {
        locked = false;
      }
    }
    const isActiveGoal = false;
    let goalBg = '';
    let goalBgLight = '';
    switch (leftSideActiveGoals) {
      case 'goal-1':
        goalBg = '#8eac99';
        goalBgLight = '#BCD9C7';
        break;
      case 'goal-2':
        goalBg = '#C0AA5D';
        goalBgLight = '#ebd486';
        break;
      case 'goal-3':
        goalBg = '#CC9C9F';
        goalBgLight = '#f1c4c7';
        break;
      case 'goal-4':
        goalBg = '#82ABC2';
        goalBgLight = '#B4D7EB';
        break;
      default:
        goalBg = '';
        goalBgLight = '';
    }
    return (
      <>
        <div className="d-block" onClick={this.scrollSection}>
          {item.type === 'button' && (
            <li
              id={`${item.section}Side`}
              className={`${Style.menuList} ${
                !isSidebarOpen && Style.collapseSidebar
              } ${
                isActiveGoal
                  ? leftSideActiveGoals === 'goal-1'
                    ? Style.goal1HoverMenuList
                    : leftSideActiveGoals === 'goal-2'
                      ? Style.goal2HoverMenuList
                      : leftSideActiveGoals === 'goal-3'
                        ? Style.goal3HoverMenuList
                        : leftSideActiveGoals === 'goal-4'
                          ? Style.goal4HoverMenuList
                          : Style.inActiveGoal
                  : Style.inActiveGoal
              } ${
                (!isActiveGoal && clickedItem) ||
                (!anyEditTrue && visibleObserver)
                  ? Style.darkBg
                  : sectionsData[item.section] !== 'lock'
                    ? Style.liteBg
                    : Style.showBg
              } d-flex ${disable}`}
              style={
                locked
                  ? { backgroundColor: '#ecece0' }
                  : isActiveGoal
                    ? clickedItem || (!anyEditTrue && visibleObserver)
                      ? { backgroundColor: goalBg }
                      : { backgroundColor: goalBgLight }
                    : {}
              }
            >
              {isSidebarOpen ? (
                <InlineSvg
                  src={`/assets/svg/${this.setsectionIcon(item.name)}.svg`}
                  height="18px"
                  width="18px"
                  color="#76748b"
                />
              ) : (
                <OverlayTrigger
                  placement="right"
                  popperConfig={popperConfig}
                  overlay={
                    <Tooltip className="customTooltip" id="button-tooltip">
                      {item.name}
                    </Tooltip>
                  }
                >
                  <div className="d-inline-block">
                    <InlineSvg
                      src={`/assets/svg/${this.setsectionIcon(item.name)}.svg`}
                      alt="pc"
                      height="18px"
                      width="18px"
                    />
                  </div>
                </OverlayTrigger>
              )}

              {isSidebarOpen && (
                <>
                  <div
                    className={`${
                      !isEditIconShow ? Style.buttonContainerFull : Style.wd_40
                    } `}
                  >
                    <button
                      type="button"
                      className={`${Style.listButton} text-left ${disable}`}
                      onClick={this.scrollSection}
                      data-cy="listButton"
                    >
                      <div className="d-flex align-items-center">
                        <p
                          // className={`${
                          //   isEditMode ? Style.buttonText : ''
                          // } p1 p-0 m-0`}
                          className="p1 p-0 m-0"
                        >
                          {item.name}
                        </p>
                        {this.props.hasMissingFields && (
                          <InlineSvg
                            src="/assets/svg/redInfoIcon.svg"
                            height="16px"
                            width="16px"
                            style={{
                              marginLeft: '8px',
                            }}
                          />
                        )}
                      </div>
                    </button>
                  </div>

                  {sectionsData[item.section] === 'lock' ? (
                    <div
                      className={`${Style.iconContainer} ${
                        isEditMode ? Style.wd80 : Style.wd50
                      } ${Style.hideOnHover}`}
                    >
                      <button
                        type="button"
                        className={`${Style.darkButton} text-right`}
                        style={{ marginLeft: '42px' }}
                        onClick={() => this.SetSectionOnOffStatus(item.section)}
                        data-cy="editButton"
                      >
                        Show
                      </button>
                    </div>
                  ) : (
                    isEditIconShow && (
                      <div style={{ width: '130px' }}>
                        <div
                          className={`${Style.iconContainer} ${
                            isEditMode ? Style.wd80 : Style.wd50
                          } ${
                            clickedItem || (!anyEditTrue && visibleObserver)
                              ? ''
                              : Style.hide
                          }`}
                        >
                          {!locked ? (
                            <>
                              {!isEditMode ? (
                                <div className="d-flex ">
                                  <button
                                    type="button"
                                    className={`${Style.darkButton} text-right mr-12 ${disable}`}
                                    onClick={() =>
                                      this.SetSectionOnOffStatus(item.section)
                                    }
                                    data-cy="editButton"
                                  >
                                    Hide
                                  </button>
                                  <button
                                    type="button"
                                    className={`${Style.darkButton} text-right ${disable}`}
                                    // style={{ marginTop: '4px' }}
                                    onClick={this.editHandler}
                                    data-cy="editButton"
                                  >
                                    Edit
                                  </button>
                                </div>
                              ) : (
                                <div className="d-flex flex-row">
                                  <button
                                    type="button"
                                    className={`${Style.darkButton} text-right mr-12 ${disable}`}
                                    onClick={this.saveHandler}
                                    data-cy="editSaveButton"
                                  >
                                    Save
                                  </button>
                                  <button
                                    type="button"
                                    className={Style.cancelButton}
                                    onClick={this.cancelHandler}
                                    data-cy="leftCancleButton"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              )}
                            </>
                          ) : (
                            <>
                              <div className={`${Style.iconContainer}`}>
                                <button
                                  type="button"
                                  className={`${Style.iconButton} text-right`}
                                  onClick={() => this.lockModalStatus()}
                                >
                                  <div className={`${Style.lockIconContainer}`}>
                                    <Icon
                                      icon={LockIconSvgPath}
                                      iconSize="20px"
                                      color="#b8b8b8"
                                    />
                                  </div>
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )
                  )}
                </>
              )}
            </li>
          )}
        </div>
      </>
    );
  }
}

MenuList.propTypes = {
  item: PropTypes.object.isRequired,
  isEditMode: PropTypes.func.isRequired,
  isClickedEdit: PropTypes.bool.isRequired,
  isEditIconShow: PropTypes.bool.isRequired,
  clickedItem: PropTypes.bool.isRequired,
  editSectionHandler: PropTypes.func.isRequired,
  saveSectionHandler: PropTypes.func.isRequired,
  cancelSectionHandler: PropTypes.func.isRequired,
  scrollSectionHandler: PropTypes.func.isRequired,
  linkButtonHandler: PropTypes.func.isRequired,
  alertModalHandler: PropTypes.func.isRequired,
  updateSectionLocking: PropTypes.func.isRequired,
  projectPreviewData: PropTypes.object.isRequired,
  userData: PropTypes.object.isRequired,
  toggleSectionLock: PropTypes.func.isRequired,
  updateOnOffSections: PropTypes.object.isRequired,
  leftSideActiveGoals: PropTypes.string.isRequired,
};

export default withTranslation('common')(MenuList);
