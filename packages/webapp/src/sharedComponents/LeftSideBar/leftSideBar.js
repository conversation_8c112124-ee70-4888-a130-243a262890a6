/* eslint-disable @next/next/no-img-element */
import React from 'react';
import { connect } from 'react-redux';
import { Popover, OverlayTrigger } from 'react-bootstrap';
import { withTranslation } from 'react-i18next';
import Router from 'next/router';
import PropTypes from 'prop-types';
import { get, isArray, keys, filter, matches, truncate } from 'lodash';
import * as moment from 'moment';
import options from 'configuration/options.json';
import { returnSectionNames } from 'lib/commonUtil';
import {
  setEditSection,
  setTheme,
  toggleTheme,
  updateProjectData,
  setIsClickedOnMenu,
  updateProjectMeta,
  updateSectionLocking,
  updateOnOffSections,
  setLeftSideBarActiveTab,
  fetchCollaboratorList,
  createCollaborator,
  fetchCollaborator,
  sendReminderToCollaborator,
  removeSectionItems,
  deleteCollaborator,
  setActiveGoals,
  updateGoals,
  setSidebarStatus,
  setCollapseSideBarActiveTab,
  setProjectsNotificationCount,
  setProjectsNotification,
  setClickedItemOfSidebar,
} from 'reducer/project';
import { logout } from 'reducer/auth';
import Socket from 'lib/socket';
import ReactModal from 'sharedComponents/Modal/modal';
import Collaborator from './Collaborator';
import Modal from './notificationModal';
import Button from '../Button/button';
import MenuList from './menuList';
import Style from '../styles/leftSideBar.module.scss';
import Styles from '../styles/notification.module.scss';
import ColorTheme from './colorTheme';
import ShowGoals from './ShowGoals';
import ShowTemplate from './ShowTemplate';
import Footer from './footer';
import InlineSvg from 'sharedComponents/inline-svg';
import mixpanelAnalytics from 'lib/mixpanel';

// Project Creation form component
class LeftSideBar extends React.PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      clickedSection: '',
      sectionKeys: [],
      isAlertShow: false,
      notificationStatus: false,
      isSectionLock: false,
      anyEditTrue: false,
    };
  }

  componentDidMount() {
    const {
      setEditSection,
      isEditIconShow,
      setIsClickedOnMenu,
      projectPreviewData,
      userData,
      updateSectionLocking,
      setProjectsNotificationCount,
      notificationsList,
      setProjectsNotification,
    } = this.props;

    const sectionsAlreadyLock = get(projectPreviewData, 'editSections', []);
    const currentUserId = get(userData, '_id');
    const lockedSections = filter(
      sectionsAlreadyLock,
      matches({
        userId: currentUserId,
      }),
    );
    if (lockedSections.length > 0) {
      updateSectionLocking(projectPreviewData._id, 'remove', {
        section: lockedSections[0].section,
        userId: currentUserId,
      });
    }
    if (isEditIconShow) {
      this.toggleSectionVisibility();
      setEditSection('');
    }
    setIsClickedOnMenu(false);
    this.setVisibleSectionAccValue();
    let section = window.location.hash;
    if (section) {
      section = section.replace('#', '');
      setTimeout(() => {
        if (section === 'artWorkEdit') {
          this.editSectionHandler('artWork');
        } else {
          this.scrollSectionHandler(section);
        }
      }, 1500);
    }

    // Socket connection
    const socket = Socket.getInstance();
    if (socket) {
      socket.on('feedback-notification', function (response) {
        const { isNewNotification, result } = get(response, 'data', {});
        if (result) {
          let notifications = [];
          if (notificationsList.length > 0) {
            notifications = notificationsList.map((item) => {
              if (result._id === item._id) {
                return result;
              }
              return item;
            });
          } else {
            notifications.push(result);
          }
          setProjectsNotification(notifications);
        }
        if (isNewNotification) {
          setProjectsNotificationCount(isNewNotification);
        }
      });
    }
  }

  handleSectionClick = (section) => {
    this.props.onSectionClick(section); // Trigger the click handler from the parent
  };

  componentWillUnmount() {
    const socket = Socket.getInstance();
    if (socket) {
      socket.off('feedback-notification');
    }
  }

  toggleSidebar = () => {
    const { isSidebarOpen, setSidebarStatus, setLeftSideBarActiveTab } =
      this.props;
    setLeftSideBarActiveTab('project');
    setSidebarStatus(!isSidebarOpen);
  };

  // method to set the left side section visible
  setVisibleSectionAccValue = () => {
    const { projectPreviewData } = this.props;
    const sectionKeys = [];
    const keysVal = keys(projectPreviewData);
    for (const key of keysVal) {
      if (
        isArray(projectPreviewData[key]) &&
        projectPreviewData[key].length > 0
      ) {
        sectionKeys.push(key);
      } else if (!isArray(projectPreviewData[key]) && projectPreviewData[key]) {
        sectionKeys.push(key);
      }
      sectionKeys.push('salesEstimateFile');
    }

    this.setState({ sectionKeys });
  };

  // method to go to specific section on overview compoennt
  goToOverview = (section) => {
    const { projectPreviewData } = this.props;
    Router.push(
      '/project/overview/[homePage]',
      `/project/overview/${projectPreviewData._id}#${section}`,
    );
  };

  scrollSectionHandler = (section) => {
    const {
      cover,
      basicInfo,
      creativeTeam,
      projectDisc,
      videos,
      artWork,
      castMembers,
      share,
      comparableProject,
      financePlan,
      budget,
      salesEstimate,
      shareScreen,
      uploadFile,
      poster,
      setClickedItemOfSidebar,
    } = this.props;

    const scrollToElement = (ref) => {
      if (ref && ref.current) {
        const elementPosition =
          ref.current.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({
          top: elementPosition,
          behavior: 'smooth',
        });
      }
    };

    if (!shareScreen) {
      switch (section) {
        case 'cover':
          scrollToElement(cover);
          break;
        case 'basicInfo':
          scrollToElement(basicInfo);
          break;
        case 'creativeTeam':
          scrollToElement(creativeTeam);
          break;
        case 'castMembers':
          scrollToElement(castMembers);
          break;
        case 'projectDisc':
          scrollToElement(projectDisc);
          break;
        case 'artWork':
          scrollToElement(artWork);
          break;
        case 'videos':
          scrollToElement(videos);
          break;
        case 'comparableProject':
          scrollToElement(comparableProject);
          break;
        case 'financePlan':
          scrollToElement(financePlan);
          break;
        case 'budget':
          scrollToElement(budget);
          break;
        case 'salesEstimateFile':
          scrollToElement(salesEstimate);
          break;
        case 'uploadFileSection':
          scrollToElement(uploadFile);
          break;
        case 'poster':
          scrollToElement(poster);
          break;
        case 'share':
          scrollToElement(share);
          break;
        default:
        // code block
      }
    } else {
      this.goToOverview(section);
    }

    setClickedItemOfSidebar(section);

    this.setState({
      clickedSection: section,
    });
  };

  // method to open edit section
  editSectionHandler = (section) => {
    const { setEditSection, setIsClickedOnMenu } = this.props;
    const editBtn = document.getElementById(`${section}PencilBtn`);
    this.setState({ anyEditTrue: true });
    if (editBtn) editBtn.click();
    setEditSection(section);
    setIsClickedOnMenu(true);
    this.toggleSectionVisibility(section);
    setTimeout(() => {
      this.scrollSectionHandler(section);
    }, 300);
  };

  // method to save the specific edit section
  saveSectionHandler = (section) => {
    const { setIsClickedOnMenu } = this.props;
    const saveBtn = document.getElementById(`${section}Save`);
    if (saveBtn) saveBtn.click();
    setIsClickedOnMenu(false);
    this.toggleSectionVisibility();
    this.setState({ anyEditTrue: false });
    setTimeout(() => {
      const errorClassLength =
        document.getElementsByClassName('validationError').length;
      if (errorClassLength < 1) {
        this.scrollSectionHandler(section);
      }
    }, 300);
  };

  // method to cancle specific section
  cancelSectionHandler = (section) => {
    const { setEditSection, setIsClickedOnMenu } = this.props;
    const cancelBtn = document.getElementById(`${section}Cancel`);
    if (cancelBtn) cancelBtn.click();
    this.setState({ anyEditTrue: false });
    setEditSection('');
    this.setState({
      clickedSection: '',
    });
    setClickedItemOfSidebar('');
    setIsClickedOnMenu(false);
    this.toggleSectionVisibility();

    // setTimeout(() => {
    //   this.scrollSectionHandler(section);
    // }, 300);
  };

  linkButtonHandler = () => {
    const { projectPreviewData } = this.props;
    Router.push(
      '/project/share/[shareHistory]',
      `/project/share/${projectPreviewData._id}`,
    );
    mixpanelAnalytics('share_project_clicked', {
      pageName: 'profile_overview',
      redirectUrl: `/project/share/${projectPreviewData._id}`,
    });
  };

  // Show hide notification modal
  toggleNotification = () => {
    const { notificationStatus } = this.state;
    const { updateProjectMeta, projectPreviewData } = this.props;
    updateProjectMeta({ isNewNotification: false }, projectPreviewData._id);
    this.setState({ notificationStatus: !notificationStatus });
  };

  // Show hide section modal
  toggleSectionLock = () => {
    const { isSectionLock } = this.state;
    this.setState({ isSectionLock: !isSectionLock });
  };

  // method to show visibilty of specific section
  toggleSectionVisibility = (section) => {
    const { setVisibleSections } = this.props;
    let visibleSections = [];
    if (section) {
      visibleSections = [section];
    } else {
      visibleSections = returnSectionNames();
    }
    setVisibleSections(visibleSections);
  };

  alertModalHandler = (isAlertShow) => {
    this.setState({ isAlertShow });
  };

  // Calculate day and hours.
  diffDaysHours = (date) => {
    const showTime = {};
    const currentDate = moment().format('YYYY-MM-DD');
    const createdDate = moment(date).format('YYYY-MM-DD');

    const diffDate = moment(`${currentDate}`, 'YYYY-MM-DD').diff(
      moment(`${createdDate}`, 'YYYY-MM-DD'),
      'days',
    );

    if (diffDate > 1) {
      showTime.days = `${diffDate}  days ago`;
    } else {
      showTime.days = `${diffDate}  day ago`;
    }

    const currentHours = moment().format('hh');
    const createdHours = moment(date).format('hh');

    const diffHours = moment(`${currentHours}`, 'hh').diff(
      moment(`${createdHours}`, 'hh'),
      'hours',
    );

    showTime.hours = `${diffHours} hours ago`;
    showTime.numberOfDays = diffDate;

    return showTime;
  };

  // This method is used for notification modal body.
  body = () => {
    const { notificationsList, projectPreviewData } = this.props;
    const projectId = get(projectPreviewData, '_id');

    // Filter only valid notifications
    let filteredFeedback = (notificationsList || [])
      .filter(
        (item) =>
          get(item, 'projectId._id') === projectId &&
          Array.isArray(item.activities) &&
          item.activities.some(
            (a) =>
              ['letsTalk', 'tracking', 'notInterested'].includes(a.action) &&
              get(a, 'user.name'),
          ),
      )
      // Sort by latest activity time (not item.updatedAt)
      .sort((a, b) => {
        const aLast = Math.max(
          ...a.activities.map((act) => new Date(act.addedAt).getTime()),
        );
        const bLast = Math.max(
          ...b.activities.map((act) => new Date(act.addedAt).getTime()),
        );

        return aLast < bLast ? -1 : 1;
      });

    return (
      <div className="mt-2">
        {filteredFeedback.length === 0 ? (
          <div className="text-left">
            <p className="text-primary p2 pt-3">
              This is where you'll find feedback to your project when it's
              shared with decision makers.
            </p>
          </div>
        ) : (
          filteredFeedback.map((item, index) => (
            <div
              key={index}
              className="col-12 d-flex flex-column p-3 border-bottom text-left"
            >
              {item.activities
                .filter(
                  (activity) =>
                    ['letsTalk', 'tracking', 'notInterested'].includes(
                      activity.action,
                    ) && get(activity, 'user.name'),
                )
                .map((activity, idx) => {
                  const activityTime =
                    activity.updatedAt || activity.addedAt || item.updatedAt;

                  return (
                    <div key={idx} className="d-flex align-items-start mb-2">
                      {/* Profile Image */}
                      <div
                        className="rounded-circle overflow-hidden"
                        style={{ width: '35px', height: '35px', flexShrink: 0 }}
                      >
                        <img
                          src={get(
                            activity,
                            'user.profileImage',
                            '/assets/jpg/Placeholder_Avatar_320.jpg',
                          )}
                          className="w-100 h-100"
                          alt="Profile"
                          style={{ objectFit: 'cover', cursor: 'pointer' }}
                        />
                      </div>

                      {/* Text Content */}
                      <div
                        className="ml-3 d-flex flex-column"
                        style={{ flex: 1 }}
                      >
                        {activity.action === 'letsTalk' && (
                          <p
                            className="mb-1 text-dark"
                            style={{ fontSize: '14px', fontWeight: '500' }}
                          >
                            <strong>{get(activity, 'user.name')}</strong> wants
                            to talk about this project.
                          </p>
                        )}
                        {activity.action === 'tracking' && (
                          <p
                            className="mb-1 text-dark"
                            style={{ fontSize: '14px', fontWeight: '500' }}
                          >
                            <strong>{get(activity, 'user.name')}</strong> is
                            tracking this project.
                          </p>
                        )}
                        {activity.action === 'notInterested' && (
                          <p
                            className="mb-1 text-dark"
                            style={{ fontSize: '14px', fontWeight: '500' }}
                          >
                            <strong>{get(activity, 'user.name')}</strong> is not
                            interested in this project at the moment.
                          </p>
                        )}

                        {/* Status Dot & Timestamp */}
                        <div className="d-flex align-items mt-1">
                          <div
                            className="rounded-circle"
                            style={{
                              width: '8px',
                              height: '8px',
                              backgroundColor:
                                activity.action === 'letsTalk'
                                  ? '#00E5D5'
                                  : activity.action === 'tracking'
                                    ? '#EBFF29'
                                    : '#FF303D',
                            }}
                          />
                          <div className="col-11">
                            <p className="p3" style={{ color: '#858585' }}>
                              {this.diffDaysHours(activityTime).numberOfDays !==
                              0
                                ? this.diffDaysHours(activityTime).days
                                : this.diffDaysHours(activityTime).hours}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          ))
        )}
      </div>
    );
  };

  renderMenuList = (item, index) => {
    const {
      shareScreen,
      projectPreviewData,
      isEditIconShow,
      editSection,
      userData,
      isClickedOnMenu,
      updateSectionLocking,
      updateOnOffSections,
      leftSideActiveGoals,
      isSidebarOpen,
      visibleObserver,
      clickedItemSidebar,
    } = this.props;

    const { anyEditTrue } = this.state;

    return (
      <MenuList
        key={String(index)}
        clickedItem={item.section === clickedItemSidebar}
        visibleObserver={item.section === visibleObserver}
        isEditMode={item.section === editSection}
        item={item}
        isClickedEdit={isClickedOnMenu}
        shareScreen={shareScreen}
        isEditIconShow={isEditIconShow}
        editSectionHandler={this.editSectionHandler}
        saveSectionHandler={this.saveSectionHandler}
        cancelSectionHandler={this.cancelSectionHandler}
        scrollSectionHandler={this.scrollSectionHandler}
        linkButtonHandler={this.linkButtonHandler}
        alertModalHandler={this.alertModalHandler}
        updateSectionLocking={updateSectionLocking}
        projectPreviewData={projectPreviewData}
        userData={userData}
        toggleSectionLock={this.toggleSectionLock}
        updateOnOffSections={updateOnOffSections}
        leftSideActiveGoals={leftSideActiveGoals}
        isSidebarOpen={isSidebarOpen}
        anyEditTrue={anyEditTrue}
      />
    );
  };

  MyPopover = () => {
    const {
      projectPreviewData,
      fetchCollaboratorList,
      removeSectionItems,
      collaboratorList,
      createCollaborator,
      sendReminderToCollaborator,
      deleteCollaborator,
      fetchCollaborator,
      t,
      toggleTheme,
      setTheme,
      updateProjectData,
      userData,
      setLeftSideBarActiveTab,
      startTour,
      isSidebarOpen,
      collapseActiveTab,
      setCollapseSideBarActiveTab,
    } = this.props;

    return (
      <Popover id="popover-basic" className="popoverBody">
        <Popover.Title>
          {get(projectPreviewData, 'cover.title') && (
            <h2 className={`${Style.projectsTitle}`} data-cy="headerId">
              {projectPreviewData.cover.title}
            </h2>
          )}

          <p className={`${Style.registrationText} p2 mb-0`} data-cy="regNo">
            SMASH id #
            {get(projectPreviewData, 'regNo') && projectPreviewData.regNo}
          </p>
        </Popover.Title>
        <Popover.Content>
          <ColorTheme
            projectPreviewData={projectPreviewData}
            fetchCollaboratorList={fetchCollaboratorList}
            collaboratorList={collaboratorList}
            userData={userData}
            toggleTheme={toggleTheme}
            setTheme={setTheme}
            updateProjectData={updateProjectData}
            setLeftSideBarActiveTab={setLeftSideBarActiveTab}
            activeTab={collapseActiveTab}
            startTour={startTour}
            isSidebarOpen={isSidebarOpen}
            collapseActiveTab={collapseActiveTab}
            setCollapseSideBarActiveTab={setCollapseSideBarActiveTab}
          />
          <ul className={`p-0 ${Style.menuContainer}`}>
            <div className={Style.sectionBtnContainer}>
              {collapseActiveTab === 'collaboration' && (
                <Collaborator
                  fetchCollaboratorList={fetchCollaboratorList}
                  collaboratorList={collaboratorList}
                  t={t}
                  userData={userData}
                  createCollaborator={createCollaborator}
                  projectPreviewData={projectPreviewData}
                  removeSectionItems={removeSectionItems}
                  sendReminderToCollaborator={sendReminderToCollaborator}
                  fetchCollaborator={fetchCollaborator}
                  deleteCollaborator={deleteCollaborator}
                  setCollapseSideBarActiveTab={setCollapseSideBarActiveTab}
                />
              )}
              {collapseActiveTab === 'templates' && (
                <ShowTemplate
                  projectPreviewData={projectPreviewData}
                  toggleTheme={toggleTheme}
                  setTheme={setTheme}
                  updateProjectData={updateProjectData}
                  isShowTab={false}
                  setCollapseSideBarActiveTab={setCollapseSideBarActiveTab}
                />
              )}
            </div>
          </ul>
        </Popover.Content>
      </Popover>
    );
  };

  render() {
    const {
      shareScreen,
      projectPreviewData,
      fetchCollaboratorList,
      removeSectionItems,
      collaboratorList,
      createCollaborator,
      sendReminderToCollaborator,
      deleteCollaborator,
      fetchCollaborator,
      t,
      isEditIconShow,
      toggleTheme,
      setTheme,
      updateProjectData,
      logout,
      userData,
      footHide,
      isNewNotification,
      activeTab,
      setLeftSideBarActiveTab,
      startTour,
      setActiveGoals,
      updateGoals,
      isSidebarOpen,
      setCollapseSideBarActiveTab,
    } = this.props;

    const { sectionKeys, isAlertShow, notificationStatus, isSectionLock } =
      this.state;
    const sectionsGoal = get(projectPreviewData, 'goals.goal', false);

    const menu = options.leftSidebarMenuList.map((item, index) => {
      if (
        (!shareScreen && isEditIconShow) ||
        sectionKeys.includes(item.section) ||
        shareScreen
      ) {
        return this.renderMenuList(item, index);
      }
      return null;
    });
    return (
      <div>
        <section className={Style.leftSideBarContainer}>
          <ReactModal
            isShowCrossBtn={false}
            titleClass="text-warning"
            modalShow={isAlertShow}
            title="Warning!"
            body="Save or close the current section before you start editing a new one."
            successBtnText="Ok"
            modalSize="md"
            successCallback={() => this.setState({ isAlertShow: false })}
          />
          <ReactModal
            isShowCrossBtn
            modalShow={isSectionLock}
            title="section locked"
            body="This section is locked as it is being edited by a collaborator. Please try again later."
            successBtnText="Ok"
            modalSize="md"
            successCallback={() => this.toggleSectionLock()}
          />
          <div className={Style.infoBar}>
            <div
              className={`row mt-24 px-3 ${
                isSidebarOpen
                  ? 'justify-content-between '
                  : 'justify-content-center'
              }`}
            >
              <div
                className={`${isSidebarOpen ? 'col-12 col-md-10 col-lg-10 p-0' : ''}`}
              >
                {get(projectPreviewData, 'cover.title') && isSidebarOpen && (
                  <h2 className={`${Style.projectsTitle}`} data-cy="headerId">
                    {truncate(projectPreviewData.cover.title, {
                      length: 30,
                      omission: '...',
                    })}
                  </h2>
                )}
              </div>
              <div
                className={`${isSidebarOpen ? 'col-12 col-md-2 col-lg-2 p-0 text-md-right text-lg-right position-relative' : 'p-0'}`}
              >
                <InlineSvg
                  src="/assets/svg/Bell.svg"
                  height="28px"
                  width="28px"
                  className={`${!isSidebarOpen && 'mb-3'}`}
                  alt=""
                  style={{ cursor: 'pointer' }}
                  onClick={() => this.toggleNotification()}
                />
                {isNewNotification && (
                  <div
                    className={`${Styles.notificationCount} carousel-caption text-light rounded-circle p4`}
                    onClick={() => this.toggleNotification()}
                  >
                    1
                  </div>
                )}
              </div>
              <Modal
                modalShow={notificationStatus}
                isSidebarOpen={isSidebarOpen}
                title="Feedback"
                body={this.body()}
                closeCallback={this.toggleNotification}
                isShowCrossBtn
              />
            </div>

            <div
              className={`row pb-3 ${
                isSidebarOpen
                  ? 'justify-content-between px-3'
                  : 'justify-content-center'
              }`}
            >
              {isSidebarOpen && (
                <p
                  className={`${Style.registrationText} p2 mb-0`}
                  data-cy="regNo"
                >
                  SMASH id #
                  {get(projectPreviewData, 'regNo') && projectPreviewData.regNo}
                </p>
              )}
              <InlineSvg
                src={`/assets/svg/${
                  !isSidebarOpen ? 'open.svg' : 'collapse.svg'
                }`}
                height="24px"
                width="24px"
                style={{ cursor: 'pointer' }}
                onClick={this.toggleSidebar}
              />
            </div>

            {!footHide && (
              <>
                {isSidebarOpen ? (
                  <div className="row border-top justify-content-center">
                    <ColorTheme
                      projectPreviewData={projectPreviewData}
                      fetchCollaboratorList={fetchCollaboratorList}
                      collaboratorList={collaboratorList}
                      userData={userData}
                      toggleTheme={toggleTheme}
                      setTheme={setTheme}
                      updateProjectData={updateProjectData}
                      setLeftSideBarActiveTab={setLeftSideBarActiveTab}
                      activeTab={activeTab}
                      startTour={startTour}
                      isSidebarOpen={isSidebarOpen}
                      setCollapseSideBarActiveTab={setCollapseSideBarActiveTab}
                    />
                  </div>
                ) : (
                  <OverlayTrigger
                    trigger="click"
                    placement="right"
                    overlay={this.MyPopover(projectPreviewData)}
                  >
                    <div className="row border-top justify-content-center">
                      <button type="button" className="btnClass">
                        <InlineSvg
                          src="/assets/svg/3dots.svg"
                          height="16px"
                          width="16px"
                          className="my-3"
                        />
                      </button>
                    </div>
                  </OverlayTrigger>
                )}
              </>
            )}
          </div>

          <ul
            className={`${
              isSidebarOpen ? Style.openHeight : Style.closedHeight
            } ${Style.menuContainer}`}
          >
            <div className={Style.sectionBtnContainer}>
              {activeTab === 'collaboration' ? (
                <Collaborator
                  fetchCollaboratorList={fetchCollaboratorList}
                  collaboratorList={collaboratorList}
                  t={t}
                  userData={userData}
                  createCollaborator={createCollaborator}
                  projectPreviewData={projectPreviewData}
                  removeSectionItems={removeSectionItems}
                  sendReminderToCollaborator={sendReminderToCollaborator}
                  fetchCollaborator={fetchCollaborator}
                  deleteCollaborator={deleteCollaborator}
                />
              ) : activeTab === 'goals-tab' && sectionsGoal ? (
                <ShowGoals
                  startTour={startTour}
                  setLeftSideBarActiveTab={setLeftSideBarActiveTab}
                  setActiveGoals={setActiveGoals}
                  projectPreviewData={projectPreviewData}
                  updateGoals={updateGoals}
                />
              ) : activeTab === 'templates' ? (
                <ShowTemplate
                  projectPreviewData={projectPreviewData}
                  toggleTheme={toggleTheme}
                  setTheme={setTheme}
                  updateProjectData={updateProjectData}
                  isShowTab={false}
                />
              ) : (
                menu
              )}
            </div>
          </ul>
        </section>
        {!footHide && (
          <div className={Style.leftSideFooter}>
            <div className="p-3">
              {isSidebarOpen ? (
                <div className="row justify-content-center p-3">
                  <Button
                    btntype="snap"
                    id="snapbutton"
                    size="large"
                    customClass={
                      get(projectPreviewData, '_id')
                        ? '--primaryNavy'
                        : '--disabled'
                    }
                    buttonValue="CREATE SNAPSHOT"
                    clickHandler={() => this.linkButtonHandler()}
                  />
                </div>
              ) : (
                <div className="row justify-content-center">
                  <InlineSvg
                    width="50px"
                    height="50px"
                    src="/assets/svg/share.svg"
                    onClick={() => this.linkButtonHandler()}
                  />
                </div>
              )}
            </div>
            <hr className={Style.hrLine} />
            <Footer logout={logout} userData={userData} />
          </div>
        )}
      </div>
    );
  }
}

LeftSideBar.propTypes = {
  isEditIconShow: PropTypes.bool.isRequired,
  setActiveGoals: PropTypes.func.isRequired,
  cover: PropTypes.object.isRequired,
  basicInfo: PropTypes.object.isRequired,
  creativeTeam: PropTypes.object.isRequired,
  projectDisc: PropTypes.object.isRequired,
  videos: PropTypes.object.isRequired,
  artWork: PropTypes.object.isRequired,
  castMembers: PropTypes.object.isRequired,
  uploadFile: PropTypes.object.isRequired,
  share: PropTypes.object.isRequired,
  comparableProject: PropTypes.object.isRequired,
  financePlan: PropTypes.object.isRequired,
  budget: PropTypes.object.isRequired,
  shareScreen: PropTypes.bool.isRequired,
  projectPreviewData: PropTypes.object.isRequired,
  setVisibleSections: PropTypes.object.isRequired,
  salesEstimate: PropTypes.object.isRequired,
  editSection: PropTypes.string.isRequired,
  setEditSection: PropTypes.func.isRequired,
  toggleTheme: PropTypes.func.isRequired,
  setTheme: PropTypes.func.isRequired,
  updateProjectData: PropTypes.func.isRequired,
  logout: PropTypes.func.isRequired,
  userData: PropTypes.object.isRequired,
  footHide: PropTypes.bool.isRequired,
  setIsClickedOnMenu: PropTypes.func.isRequired,
  isClickedOnMenu: PropTypes.bool.isRequired,
  notificationsList: PropTypes.array.isRequired,
  updateProjectMeta: PropTypes.func.isRequired,
  isNewNotification: PropTypes.number.isRequired,
  updateSectionLocking: PropTypes.func.isRequired,
  poster: PropTypes.object.isRequired,
  updateOnOffSections: PropTypes.object.isRequired,
  activeTab: PropTypes.object.isRequired,
  setLeftSideBarActiveTab: PropTypes.func.isRequired,
  startTour: PropTypes.func.isRequired,
  fetchCollaboratorList: PropTypes.func.isRequired,
  collaboratorList: PropTypes.func.isRequired,
  createCollaborator: PropTypes.func.isRequired,
  t: PropTypes.object.isRequired,
  sendReminderToCollaborator: PropTypes.func.isRequired,
  fetchCollaborator: PropTypes.func.isRequired,
  deleteCollaborator: PropTypes.func.isRequired,
  removeSectionItems: PropTypes.func.isRequired,
  leftSideActiveGoals: PropTypes.string.isRequired,
  updateGoals: PropTypes.func.isRequired,
};

const mapStateToProps = (state) => ({
  editSection: state.project.editSection,
  userData: state.auth.userData,
  isClickedOnMenu: state.project.isClickedOnMenu,
  isNewNotification: state.project.isNewNotification,
  notificationsList: state.project.notificationsList,
  activeTab: state.project.activeTab,
  collaboratorList: state.project.collaboratorList,
  leftSideActiveGoals: state.project.leftSideActiveGoals,
  isSidebarOpen: state.project.isSidebarOpen,
  collapseActiveTab: state.project.collapseActiveTab,
  clickedItemSidebar: state.project.clickedItemSidebar,
});

const mapDispatchToProps = (dispatch) => {
  return {
    setEditSection: (payload) => dispatch(setEditSection(payload)),
    toggleTheme: (payload) => dispatch(toggleTheme(payload)),
    updateProjectData: (value, id) => dispatch(updateProjectData(value, id)),
    setTheme: (payload) => dispatch(setTheme(payload)),
    logout: (payload) => dispatch(logout(payload)),
    setIsClickedOnMenu: (payload) => dispatch(setIsClickedOnMenu(payload)),
    updateProjectMeta: (payload, id) =>
      dispatch(updateProjectMeta(payload, id)),
    updateSectionLocking: (id, action, payload) =>
      dispatch(updateSectionLocking(id, action, payload)),
    updateOnOffSections: (payload, id) =>
      dispatch(updateOnOffSections(payload, id)),
    setLeftSideBarActiveTab: (payload) =>
      dispatch(setLeftSideBarActiveTab(payload)),
    fetchCollaboratorList: (payload) =>
      dispatch(fetchCollaboratorList(payload)),
    createCollaborator: (payload) => dispatch(createCollaborator(payload)),
    sendReminderToCollaborator: (value, id) =>
      dispatch(sendReminderToCollaborator(value, id)),
    fetchCollaborator: (payload) => dispatch(fetchCollaborator(payload)),
    deleteCollaborator: (id) => dispatch(deleteCollaborator(id)),
    removeSectionItems: (projectId, section, sectionObjid) =>
      dispatch(removeSectionItems(projectId, section, sectionObjid)),
    setActiveGoals: (payload) => dispatch(setActiveGoals(payload)),
    updateGoals: (goal, id) => dispatch(updateGoals(goal, id)),
    setSidebarStatus: (payload) => dispatch(setSidebarStatus(payload)),
    setCollapseSideBarActiveTab: (payload) =>
      dispatch(setCollapseSideBarActiveTab(payload)),
    setProjectsNotificationCount: (payload) =>
      dispatch(setProjectsNotificationCount(payload)),
    setProjectsNotification: (payload) =>
      dispatch(setProjectsNotification(payload)),
    setClickedItemOfSidebar: (payload) =>
      dispatch(setClickedItemOfSidebar(payload)),
  };
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(withTranslation('common')(LeftSideBar));
