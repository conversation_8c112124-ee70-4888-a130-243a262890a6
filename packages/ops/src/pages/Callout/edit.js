import React, { useEffect, useState } from 'react';
import { get, isEmpty } from 'lodash';
import {
  Title,
  useRedirect,
  useNotify,
  useUpdate,
  useGetRecordId,
  useGetOne,
  Confirm,
  useDataProvider,
} from 'react-admin';
import { Card, CardContent, Button, Box } from '@mui/material';
import { removeHtmlTags } from '../../helpers/helper';
import CustomEditor from '../../sharedComponents/editor';
import BasicBreadcrumbs from '../../sharedComponents/basicBreadCrumbs';
// import ViewPageSkelton from '../../sharedComponents/viewpageSkelton';

const EditCallOut = () => {
  const [update] = useUpdate();
  const [open, setOpen] = useState(false);
  const notify = useNotify();
  const redirect = useRedirect();
  const recordId = useGetRecordId();
  const { data, isLoading } = useGetOne('callouts', {
    id: recordId,
  });

  const newCallout = false;
  const dataProvider = useDataProvider();

  const callout = data;
  const body = get(callout, 'body', {});
  const [content, setContent] = useState({});

  useEffect(() => {
    if (!isEmpty(body)) {
      setContent(body);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [body]);

  // if (isLoading) {
  //   return <ViewPageSkelton />;
  // }

  if (isEmpty(callout)) return null;

  const handleOnChange = (editorData) => {
    editorData.logo = get(body, 'logo');
    editorData.companyName = get(body, 'companyName');
    setContent(editorData);
  };

  //handle switch input details
  const handleInputDetails = () => {
    redirect(`/callouts/${data.id}/create`);
  };

  const save = () => {
    const saveData = {
      name: content.title ? removeHtmlTags(content.title) : 'UNTITLED',
      isPublished: get(callout, 'isPublished', false),
      body: {
        logo: get(content, 'logo', body.logo),
        title: content.title || 'UNTITLED',
        ...(content.content ? { content: content.content } : {}),
        ...(body.companyName
          ? { companyName: get(body, 'companyName', null) }
          : {}),
        ...(content.companyProfileHeading
          ? { companyProfileHeading: content.companyProfileHeading }
          : {}),
        ...(content.companyProfile
          ? { companyProfile: content.companyProfile }
          : {}),
      },
      requiredFields: {
        cover: {
          coverImage: false,
          title: false,
          producer: false,
          director: true,
          writer: false,
        },
      },
    };
    update(
      'v1/callout',
      {
        id: recordId,
        data: saveData,
        previousData: body,
      },
      {
        onSuccess: (res) => {
          notify("Callout's created successfully", {
            type: 'success',
            multiLine: true,
            autoHideDuration: 2500,
          });
          redirect(`/callouts/${res.data._id}/show`);
        },
        onError: (error) => {
          notify(`${String(error)}`, {
            type: 'error',
            multiLine: true,
            autoHideDuration: 2500,
          });
        },
      }
    );
  };

  const saveAndPublish = () => {
    const publishData = {
      name: content.title ? removeHtmlTags(content.title) : 'UNTITLED',
      isPublished: true,
      body: {
        logo: get(content, 'logo', body.logo),
        title: content.title || 'UNTITLED',
        ...(content.content ? { content: content.content } : {}),
        ...(body.companyName
          ? { companyName: get(body, 'companyName', null) }
          : {}),
        ...(content.companyProfileHeading
          ? { companyProfileHeading: content.companyProfileHeading }
          : {}),
        ...(content.companyProfile
          ? { companyProfile: content.companyProfile }
          : {}),
      },
    };
    dataProvider
      .update('v1/callout', {
        id: recordId,
        data: publishData,
        previousData: body,
      })
      .then(({ data }) => {
        notify("Callout's created and published successfully", {
          type: 'success',
          multiLine: true,
          autoHideDuration: 2500,
        });
        redirect(`/callouts/${data.id}/show`);
      })
      .catch((error) => {
        notify(`${String(error)}`, {
          type: 'error',
          multiLine: true,
          autoHideDuration: 2500,
        });
      });
  };

  //handle discard changes
  const handleDiscardChanges = () => {
    redirect(`/callouts/${recordId}/show`);
  };

  const updatePublish = () => setOpen(true);
  const handleDialogClose = () => setOpen(false);
  const confirmTitle = 'Are you sure you want to publish this call out?';
  const confirmContent =
    'Publishing this call out will display it in the Smash platform and allow users to submit their projects.';

  return (
    <>
      <Title title="Edit CallOuts" />
      <div className="pt-3">
        <BasicBreadcrumbs
          links={[
            { label: 'Call outs', url: '/callouts' },
            {
              label: get(data, 'name'),
              url: `/callouts/${get(callout, 'id')}/show`,
            },
            { label: 'Edit' },
          ]}
        />
        <div className="mt-3 mb-2">
          <h6>Edit call out</h6>
        </div>
      </div>
      <Card>
        <CardContent style={{ maxWidth: '640px', margin: 'auto' }}>
          <CustomEditor
            logo={get(body, 'logo')}
            initialData={body}
            onChange={handleOnChange}
          />
        </CardContent>
      </Card>
      <Box
        direction="row"
        gap={2}
        mt={3}
        sx={{ mb: 3, justifyContent: 'end', display: 'flex' }}
      >
        <Button
          className="ml-3"
          variant="outlined"
          color="inherit"
          onClick={handleInputDetails}
          style={{
            textTransform: 'none',
            fontWeight: 'bold',
          }}
        >
          Switch to inputted details
        </Button>

        {!newCallout && (
          <Button
            className="ml-3"
            variant="outlined"
            color="inherit"
            onClick={handleDiscardChanges}
            style={{
              textTransform: 'none',
              fontWeight: 'bold',
            }}
          >
            Discard changes
          </Button>
        )}
        <Button
          className="ml-3"
          variant="contained"
          onClick={save}
          style={{
            textTransform: 'none',
            fontWeight: 'bold',
          }}
        >
          Save
        </Button>
        <Button
          className="ml-3"
          variant="contained"
          onClick={updatePublish}
          style={{
            textTransform: 'none',
            fontWeight: 'bold',
          }}
        >
          Save & publish
        </Button>
        <Confirm
          isOpen={open}
          loading={isLoading}
          title={confirmTitle}
          content={confirmContent}
          onConfirm={saveAndPublish}
          onClose={handleDialogClose}
        />
      </Box>
    </>
  );
};

export default EditCallOut;
