const CustomerIo = require('../../config/customerio');
const Boom = require('@hapi/boom');
const moment = require('moment');
const { get } = require('lodash');

class CRMMailService {
  /**
   * This function is responsible for tracking users with Customer.io.
   */
  static async trackUsersCustomerIo(user) {
    try {
      global.logger().info(`gettyInfoMailSend method called`);
      const id = get(user, 'id');
      const email = get(user, 'email');
      const firstName = get(user, 'fname');
      const lastName = get(user, 'lname');
      const occupation = get(user, 'occupation');
      const imdbLink = get(user, 'IMBDlink');
      const location = get(user, 'address');
      const privacyPolicy = get(user, 'agreeTermsPrivacy');
      const projectNotifications = get(user, 'acceptProjectNotifications');
      const easyAccess = get(user, 'easyAccessPermission');
      const emailMarketing = get(user, 'receiveMarketMaterial');
      const cio = await CustomerIo.getInstance();

      await cio.identify(id, {
        email,
        created_at: moment().unix(),
        first_name: firstName,
        last_name: lastName,
        privacy_policy: privacyPolicy,
        project_notifications: projectNotifications,
        getty_easy_access: easyAccess,
        email_marketing: emailMarketing,
        registration_completed: true,
        imdb: imdbLink,
        occupation,
        location,
        plan_name: 'free',
      });
    } catch (err) {
      global
        .logger()
        .error(
          `CRMMailService.gettyInfoMailSend getting error: ${err.message}`,
        );
      throw new Boom.Boom(err, { statusCode: 500 });
    }
  }

  /**
   * This function is responsible for tracking events via Customer.io
   */

  static async trackEvent(eventName, data) {
    try {
      const cio = await CustomerIo.getInstance();
      const payload = {
        name: eventName,
        data,
      };
      Object.keys(payload.data).forEach((key) => {
        if (payload.data[key] === undefined || payload.data[key] === null) {
          delete payload.data[key];
        }
      });
      await cio.track(get(data, 'user_id'), payload);
    } catch (err) {
      global.logger().error(`CRMMailService.trackEvent error: ${err.message}`);
      throw new Boom.Boom(err, { statusCode: 500 });
    }
  }
}

module.exports = CRMMailService;
