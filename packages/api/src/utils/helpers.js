const { get, find, startCase, toLower } = require('lodash');
const mongoose = require('mongoose');

/**
 * This function is validate file type according to given extension
 *
 * @param {File} file uploaded file
 * @param {String} exceptType file type for validation
 *
 * @return {Boolean} true/false valid or not
 */

const isValidFile = async (
  file,
  exceptType = ['jpeg', 'jpg', 'png', 'svg'],
) => {
  const name = file.hapi.filename;
  const extension = name.split('.').pop().toLowerCase();
  if (exceptType.includes(extension)) {
    global.logger().info('Valid file!');
    return true;
  }
  global.logger().info('Invalid file!');
  return false;
};

/**
 * This method is responsible to format user full
 *
 * @param {*} user user object
 * @return {String} user full name
 */
const getFullName = (user) => {
  if (user && find(user.profile, 'firstName')) {
    return `${startCase(toLower(user.profile.name.firstName))} ${startCase(
      toLower(user.profile.name.lastName),
    )}`;
  } else if (user) {
    return `${user.email}`;
  } else {
    return 'Unknown';
  }
};

/**
 * This method is responsible for create slug
 *
 * @param {*} string raw string
 * @return {*} string slug
 */
const getSlug = async (string) => {
  return string
    .toLowerCase()
    .replace(/ /g, '_')
    .replace(/[^\w-]+/g, '');
};

/**
 * This method is responsible to return creator object using user object
 *
 * @param {*} user user object
 * @return {*} creator object
 */
const getCreatorFormateObject = (user) => {
  return {
    userId: user._id,
    username: getFullName(user),
    email: user.email,
    avatar: get(user, 'profile.profileImage', null),
  };
};

/** This method is responsible to return discoverer object using discoverer object
 *
 * @param {*} user discoverer object
 * @return {*} creator object
 */
const getDiscovererFormateObject = (user) => {
  return {
    id: user._id,
    name: getFullName(user),
    avatar: get(user, 'profile.profileImage', null),
    email: user.email,
  };
};

/**
 * This method is responsible to convert string to mongodb object id
 *
 * @param {string} id - string id
 * @return {any} - mongodb object id
 */
const convertStringToMongodbId = (id) => {
  return mongoose.Types.ObjectId(id);
};

/**
 * This method is responsible to return payment info
 *
 * @param {*} invoice invoice object
 * @return {*} payment info object
 */
const getPaymentInfo = (invoice) => {
  return {
    subscriptionId: invoice.id,
    currency: get(invoice, 'currency'),
    amount_due: get(invoice, 'amount_due'),
    subtotal: get(invoice, 'subtotal'),
    clientSecret: get(invoice, 'latest_invoice.payment_intent.client_secret'),
  };
};

/**
 *  A slate status code
 * @param {string} status
 * @returns {string}
 */
const getSlateStatusLabel = (status) => {
  switch (status) {
    case 'tracking':
      return 'Tracking';
    case 'lets_talk':
      return 'Lets Talk';
    case 'not_interested':
      return 'Not Interested';
    default:
      return status;
  }
};

/**
 * Helper function to get default required fields structure
 * @returns {Object} Default required fields object
 */
const getDefaultRequiredFields = () => ({
  cover: {
    coverImage: false,
    title: false,
    producer: false,
    director: false,
    writer: false,
  },
  basicInformation: {
    logline: false,
    tags: false,
    genre: false,
    setting: false,
    runtime: false,
    status: false,
    projectHighlights: false,
  },
  description: {
    synopsis: false,
    creativeVision: false,
    treatmentBible: false,
    script: false,
  },
  creativeTeam: false,
  castMembers: false,
  moodboard: false,
  projectVideos: false,
  financePlan: false,
  budget: false,
  otherDocuments: false,
  salesEstimate: false,
});

module.exports = {
  isValidFile,
  getFullName,
  getSlug,
  getPaymentInfo,
  getCreatorFormateObject,
  getDiscovererFormateObject,
  convertStringToMongodbId,
  getSlateStatusLabel,
  getDefaultRequiredFields,
};
