const mongoose = require('mongoose');
const { Schema } = mongoose;
const mongoosePaginate = require('mongoose-paginate');
const {
  UserSchema,
  DiscovererSchema,
  CitySchema,
  // SnapshotSchema,
} = require('./common');

// Remove embedded SubmissionSchema and SlateSchema

const CalloutBodySchema = new Schema({
  _id: false,
  companyName: { type: String },
  companyProfileHeading: { type: String },
  companyProfile: { type: String },
  content: { type: String },
  logo: { type: String },
  title: { type: String },
});

// Sub-schema for required fields
const RequiredFieldsSchema = new Schema({
  _id: false,
  cover: {
    coverImage: { type: Boolean, default: false },
    title: { type: Boolean, default: false },
    producer: { type: Boolean, default: false },
    director: { type: Boolean, default: false },
    writer: { type: Boolean, default: false },
  },
  basicInformation: {
    logline: { type: Boolean, default: false },
    tags: { type: Boolean, default: false },
    genre: { type: Boolean, default: false },
    setting: { type: Boolean, default: false },
    runtime: { type: Boolean, default: false },
    status: { type: Boolean, default: false },
    projectHighlights: { type: Boolean, default: false },
  },
  description: {
    synopsis: { type: Boolean, default: false },
    creativeVision: { type: Boolean, default: false },
    treatmentBible: { type: Boolean, default: false },
    script: { type: Boolean, default: false },
  },
  creativeTeam: { type: Boolean, default: false },
  castMembers: { type: Boolean, default: false },
  moodboard: { type: Boolean, default: false },
  projectVideos: { type: Boolean, default: false },
  financePlan: { type: Boolean, default: false },
  budget: { type: Boolean, default: false },
  otherDocuments: { type: Boolean, default: false },
  salesEstimate: { type: Boolean, default: false },
});

const CalloutSchema = new Schema(
  {
    name: { type: String },
    body: CalloutBodySchema,
    genres: { type: String },
    opportunities: { type: String },
    budget: { type: Number, default: 0 },
    city: CitySchema,
    creator: UserSchema,
    discoverer: DiscovererSchema,
    prompt: { type: String },
    submissions: [{ type: Schema.Types.ObjectId, ref: 'Submission' }],
    totalSubmissions: { type: Number, default: 0 },
    slates: [{ type: Schema.Types.ObjectId, ref: 'Submission' }],
    totalSlates: { type: Number, default: 0 },
    deleted: { type: Boolean, default: false },
    isPublished: { type: Boolean, default: false },
    showOrganisationName: { type: Boolean, default: true },
    status: { type: String, enum: ['public', 'private'], default: 'private' },
    requiredFields: RequiredFieldsSchema,
  },
  { timestamps: true },
);
CalloutSchema.plugin(mongoosePaginate);
const Callout = mongoose.model('Callout', CalloutSchema);
module.exports = Callout;
